const http = require('http');
const https = require('https');
const net = require('net');
const url = require('url');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 目标域名列表
const TARGET_DOMAINS = [
    'login.live.com',
    'outlook.live.com',
    'account.live.com'
];

// 创建保存目录
const SAVE_DIR = path.join(__dirname, 'captured_data');
if (!fs.existsSync(SAVE_DIR)) {
    fs.mkdirSync(SAVE_DIR, { recursive: true });
}

// 生成文件名
function generateFileName(domain, method, timestamp) {
    const date = new Date(timestamp);
    const dateStr = date.toISOString().replace(/[:.]/g, '-').substring(0, 19);
    const randomId = crypto.randomBytes(4).toString('hex');
    return `${domain}_${method}_${dateStr}_${randomId}.txt`;
}

// 保存数据到文件
function saveToFile(domain, method, requestData, responseData, url) {
    const timestamp = Date.now();
    const fileName = generateFileName(domain, method, timestamp);
    const filePath = path.join(SAVE_DIR, fileName);
    
    const separator = '='.repeat(80);
    const content = `${separator}\n` +
                   `抓包时间: ${new Date(timestamp).toLocaleString('zh-CN')}\n` +
                   `目标域名: ${domain}\n` +
                   `请求方法: ${method}\n` +
                   `请求URL: ${url}\n` +
                   `${separator}\n\n` +
                   `【请求数据】\n${separator}\n${requestData}\n\n` +
                   `【响应数据】\n${separator}\n${responseData}\n\n` +
                   `${separator}\n` +
                   `抓包结束\n` +
                   `${separator}\n`;
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ 数据已保存: ${fileName}`);
    return filePath;
}

// 检查是否为目标域名
function isTargetDomain(hostname) {
    if (!hostname) return false;
    return TARGET_DOMAINS.some(domain => 
        hostname === domain || hostname.endsWith('.' + domain)
    );
}

// 格式化请求头
function formatHeaders(headers) {
    return Object.keys(headers)
        .map(key => `${key}: ${headers[key]}`)
        .join('\n');
}

// 处理HTTP请求
function handleHttpRequest(req, res) {
    const urlParts = url.parse(req.url);
    const hostname = urlParts.hostname || req.headers.host;
    
    console.log(`🌐 HTTP请求: ${req.method} ${hostname}${urlParts.path || ''}`);
    
    if (!isTargetDomain(hostname)) {
        res.writeHead(200);
        res.end('非目标域名，已忽略');
        return;
    }
    
    console.log(`🎯 捕获目标域名: ${hostname}`);
    
    // 收集请求数据
    let requestBody = '';
    req.on('data', chunk => {
        requestBody += chunk.toString();
    });
    
    req.on('end', () => {
        const requestData = `${req.method} ${req.url} HTTP/${req.httpVersion}\n` +
                          `Host: ${hostname}\n` +
                          `${formatHeaders(req.headers)}\n\n` +
                          `${requestBody}`;
        
        // 转发请求到目标服务器
        const options = {
            hostname: hostname,
            port: urlParts.port || 80,
            path: urlParts.path || '/',
            method: req.method,
            headers: req.headers
        };
        
        const proxyReq = http.request(options, (proxyRes) => {
            // 设置响应头
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            
            let responseBody = '';
            
            proxyRes.on('data', chunk => {
                responseBody += chunk.toString();
                res.write(chunk);
            });
            
            proxyRes.on('end', () => {
                const responseData = `HTTP/${proxyRes.httpVersion} ${proxyRes.statusCode} ${proxyRes.statusMessage}\n` +
                                   `${formatHeaders(proxyRes.headers)}\n\n` +
                                   `${responseBody}`;
                
                // 保存数据
                saveToFile(hostname, req.method, requestData, responseData, req.url);
                res.end();
            });
        });
        
        proxyReq.on('error', (err) => {
            console.error('❌ 代理请求错误:', err.message);
            res.writeHead(500);
            res.end('代理错误: ' + err.message);
        });
        
        if (requestBody) {
            proxyReq.write(requestBody);
        }
        proxyReq.end();
    });
}

// 处理HTTPS CONNECT请求
function handleHttpsConnect(req, clientSocket, head) {
    const [hostname, port] = req.url.split(':');
    
    console.log(`🔒 HTTPS CONNECT: ${hostname}:${port || 443}`);
    
    if (!isTargetDomain(hostname)) {
        // 非目标域名，建立透明隧道
        const serverSocket = net.connect(port || 443, hostname, () => {
            clientSocket.write('HTTP/1.1 200 Connection Established\r\n\r\n');
            serverSocket.pipe(clientSocket);
            clientSocket.pipe(serverSocket);
        });
        
        serverSocket.on('error', (err) => {
            console.error('❌ 隧道连接错误:', err.message);
            clientSocket.end();
        });
        return;
    }
    
    console.log(`🎯 捕获HTTPS目标域名: ${hostname}`);
    
    // 对于目标域名，建立监听隧道
    const serverSocket = net.connect(port || 443, hostname, () => {
        clientSocket.write('HTTP/1.1 200 Connection Established\r\n\r\n');
        
        let clientData = Buffer.alloc(0);
        let serverData = Buffer.alloc(0);
        
        // 监听客户端到服务器的数据
        clientSocket.on('data', (data) => {
            clientData = Buffer.concat([clientData, data]);
            serverSocket.write(data);
        });
        
        // 监听服务器到客户端的数据
        serverSocket.on('data', (data) => {
            serverData = Buffer.concat([serverData, data]);
            clientSocket.write(data);
        });
        
        // 连接结束时保存数据
        const cleanup = () => {
            if (clientData.length > 0 || serverData.length > 0) {
                const timestamp = Date.now();
                
                // 尝试解析TLS握手信息
                const clientInfo = parseTLSData(clientData);
                const serverInfo = parseTLSData(serverData);
                
                const requestData = `HTTPS隧道 - 客户端数据 (${clientData.length} 字节)\n` +
                                   `TLS信息: ${clientInfo}\n` +
                                   `原始数据 (前1000字节): ${clientData.slice(0, 1000).toString('hex')}\n`;
                
                const responseData = `HTTPS隧道 - 服务器数据 (${serverData.length} 字节)\n` +
                                    `TLS信息: ${serverInfo}\n` +
                                    `原始数据 (前1000字节): ${serverData.slice(0, 1000).toString('hex')}\n`;
                
                saveToFile(hostname, 'CONNECT', requestData, responseData, `https://${hostname}:${port || 443}`);
            }
        };
        
        clientSocket.on('end', cleanup);
        serverSocket.on('end', cleanup);
        clientSocket.on('close', cleanup);
        serverSocket.on('close', cleanup);
    });
    
    serverSocket.on('error', (err) => {
        console.error('❌ HTTPS服务器连接错误:', err.message);
        clientSocket.end();
    });
}

// 简单的TLS数据解析
function parseTLSData(data) {
    if (data.length < 5) return '数据太短';
    
    const contentType = data[0];
    const version = (data[1] << 8) | data[2];
    const length = (data[3] << 8) | data[4];
    
    const types = {
        20: 'Change Cipher Spec',
        21: 'Alert',
        22: 'Handshake',
        23: 'Application Data'
    };
    
    const versions = {
        0x0301: 'TLS 1.0',
        0x0302: 'TLS 1.1',
        0x0303: 'TLS 1.2',
        0x0304: 'TLS 1.3'
    };
    
    return `类型: ${types[contentType] || 'Unknown'}, 版本: ${versions[version] || 'Unknown'}, 长度: ${length}`;
}

// 创建代理服务器
const server = http.createServer(handleHttpRequest);
server.on('connect', handleHttpsConnect);

const PORT = 8888;
server.listen(PORT, () => {
    console.log(`🚀 抓包代理服务器已启动`);
    console.log(`📡 监听端口: ${PORT}`);
    console.log(`🎯 目标域名: ${TARGET_DOMAINS.join(', ')}`);
    console.log(`💾 数据保存目录: ${SAVE_DIR}`);
    console.log(`\n📋 浏览器代理设置:`);
    console.log(`   HTTP代理: 127.0.0.1:${PORT}`);
    console.log(`   HTTPS代理: 127.0.0.1:${PORT}`);
    console.log(`\n⏹️  按 Ctrl+C 停止服务器\n`);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

// 错误处理
process.on('uncaughtException', (err) => {
    console.error('❌ 未捕获的异常:', err);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
});
