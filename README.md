# 本地抓包工具

这是一个专门用于抓取指定域名HTTPS请求的Node.js工具。

## 功能特点

- 抓取指定域名的HTTP/HTTPS请求和响应
- 目标域名：
  - login.live.com
  - outlook.live.com  
  - account.live.com
- 将原始请求和响应数据保存为txt文件
- 支持HTTP和HTTPS代理

## 安装和使用

### 1. 安装依赖
```bash
npm install
```

### 2. 启动抓包工具
```bash
npm start
```

### 3. 配置浏览器代理
将浏览器的HTTP/HTTPS代理设置为：
- 代理服务器：127.0.0.1
- 端口：8888

#### Chrome浏览器设置步骤：
1. 打开Chrome设置
2. 搜索"代理"
3. 点击"打开您计算机的代理设置"
4. 启用"使用代理服务器"
5. 地址：127.0.0.1，端口：8888

#### Firefox浏览器设置步骤：
1. 打开Firefox设置
2. 搜索"网络设置"
3. 点击"设置"按钮
4. 选择"手动代理配置"
5. HTTP代理：127.0.0.1，端口：8888
6. 勾选"也将此代理用于HTTPS"

### 4. 访问目标网站
配置好代理后，访问以下任一网站：
- https://login.live.com
- https://outlook.live.com
- https://account.live.com

### 5. 查看抓包数据
抓包数据会自动保存在 `captured_data` 目录下，文件名格式为：
```
域名_时间戳.txt
```

## 文件结构
```
├── capture.js          # 主程序文件
├── package.json        # 项目配置
├── README.md          # 说明文档
└── captured_data/     # 抓包数据保存目录
    ├── login.live.com_2024-01-01T12-00-00-000Z.txt
    └── ...
```

## 注意事项

1. **安全提醒**：此工具仅用于学习和测试目的，请勿用于非法用途
2. **HTTPS限制**：由于HTTPS加密特性，工具只能记录加密后的数据流
3. **代理设置**：使用完毕后记得关闭浏览器代理设置
4. **防火墙**：如果遇到连接问题，请检查防火墙设置

## 停止工具
按 `Ctrl+C` 停止抓包工具并关闭代理服务器。

## 故障排除

### 问题1：无法连接到代理
- 检查端口8888是否被占用
- 确认防火墙允许该端口

### 问题2：没有抓到数据
- 确认浏览器代理设置正确
- 检查访问的域名是否在目标列表中
- 查看控制台输出信息

### 问题3：HTTPS网站无法访问
- 某些网站可能有证书验证，这是正常现象
- 可以尝试访问HTTP版本的网站进行测试
