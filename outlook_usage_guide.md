# Outlook邮箱登录和邮件获取工具使用指南

## 快速开始

### 1. 安装依赖

```bash
pip install requests beautifulsoup4
```

### 2. 基本使用

```python
from outlook_login_flow import OutlookClient

# 创建客户端
client = OutlookClient()

# 设置登录信息
username = "<EMAIL>"
password = "your_password"

# 执行登录和获取邮件
emails = client.login_and_get_emails(username, password, max_emails=10)
print("邮件获取成功！")
```

## 完整流程说明

### 参数流转图

```
第1步: OAuth初始化
├── 生成: uaid, state, nonce, code_challenge
├── 获得: PPFT令牌, MSPRequ Cookie
│
第2步: 实验配置
├── 使用: uaid (correlationId)
│
第3步: 密码验证
├── 使用: PPFT令牌 (来自第1步)
├── 使用: 用户名密码
├── 获得: 新PPFT令牌
│
第4步: 保持登录
├── 使用: 新PPFT令牌 (来自第3步)
├── 获得: 重定向表单
│
第5步: 状态保存
├── 使用: 表单数据 (来自第4步)
├── 获得: 授权码
│
第6步: 访问邮箱
├── 使用: 认证Cookie
├── 生成: 会话ID
│
第7步: 启动数据
├── 生成: MSAuth令牌, anchor_mailbox
├── 获得: 邮箱配置
│
第8步: 文件夹摘要
├── 使用: MSAuth令牌 (来自第7步)
├── 获得: 文件夹ID
│
第9步: 文件夹信息
├── 使用: 文件夹ID (来自第8步)
│
第10步: 邮件列表
└── 使用: 文件夹ID, 认证信息
```

## 关键参数详解

### OAuth参数
- **client_id**: `9199bf20-a13f-4107-85dc-02114787ef48` (固定)
- **code_challenge**: PKCE验证码，SHA256哈希
- **state**: Base64编码的状态参数，防CSRF
- **nonce**: 随机数，防重放攻击

### 认证参数
- **PPFT**: 防伪令牌，每步都会更新
- **MSAuth**: 访问Outlook的核心令牌
- **anchor_mailbox**: 用户邮箱PUID标识

### 会话参数
- **uaid**: 唯一会话标识符
- **session_id**: OWA会话ID
- **correlation_id**: 请求关联ID

## 使用示例

### 示例1: 获取最新邮件

```python
def get_latest_emails():
    client = OutlookClient()
    
    try:
        emails = client.login_and_get_emails(
            username="<EMAIL>",
            password="password123",
            max_emails=5
        )
        
        # 解析邮件
        if isinstance(emails, dict):
            items = emails.get('Body', {}).get('ResponseMessages', {}).get('Items', [])
            for item in items:
                mail_items = item.get('RootFolder', {}).get('Items', [])
                for mail in mail_items:
                    print(f"主题: {mail.get('Subject', '无主题')}")
                    print(f"发件人: {mail.get('From', {}).get('Mailbox', {}).get('EmailAddress', '未知')}")
                    print("-" * 30)
    except Exception as e:
        print(f"错误: {e}")
```

### 示例2: 分步执行

```python
def step_by_step_login():
    client = OutlookClient()
    username = "<EMAIL>"
    password = "password123"
    
    try:
        # 第1步: OAuth初始化
        client.step1_oauth_authorize(username)
        print(f"PPFT令牌: {client.ppft_token[:20]}...")
        
        # 第2步: 实验配置
        client.step2_get_experiments()
        
        # 第3步: 密码验证
        client.step3_password_auth(username, password)
        print(f"新PPFT令牌: {client.ppft_token[:20]}...")
        
        # 继续其他步骤...
        
    except Exception as e:
        print(f"登录失败: {e}")
```

## 错误处理

### 常见错误及解决方案

1. **PPFT令牌提取失败**
   ```python
   if not client.ppft_token:
       raise Exception("无法获取PPFT令牌，请检查网络连接")
   ```

2. **密码验证失败**
   ```python
   # 检查响应中的错误信息
   if "incorrect" in response.text.lower():
       raise Exception("用户名或密码错误")
   ```

3. **MSAuth令牌无效**
   ```python
   if response.status_code == 401:
       raise Exception("认证令牌无效，需要重新登录")
   ```

## 高级配置

### 自定义请求头

```python
client = OutlookClient()
client.session.headers.update({
    'User-Agent': 'Custom User Agent',
    'Accept-Language': 'en-US,en;q=0.9'
})
```

### 代理设置

```python
client.session.proxies = {
    'http': 'http://proxy:8080',
    'https': 'https://proxy:8080'
}
```

### 超时设置

```python
# 在每个请求中添加timeout参数
response = client.session.get(url, timeout=30)
```

## 注意事项

1. **安全性**: 不要在代码中硬编码密码
2. **频率限制**: 避免过于频繁的请求
3. **二次验证**: 如果启用了2FA，需要额外处理
4. **Cookie管理**: 脚本自动处理Cookie持久化
5. **错误重试**: 建议添加重试机制

## 扩展功能

基于此脚本可以扩展：

- 邮件内容获取
- 附件下载
- 邮件发送
- 文件夹操作
- 邮件搜索和过滤

## 调试技巧

### 启用详细日志

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 保存响应内容

```python
with open('response.html', 'w', encoding='utf-8') as f:
    f.write(response.text)
```

### 检查Cookie

```python
print("当前Cookie:")
for cookie in client.session.cookies:
    print(f"{cookie.name}: {cookie.value}")
```
