#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Outlook邮箱登录和邮件获取完整流程
模拟浏览器行为，从账号密码登录到获取邮件列表
"""

import requests
import re
import json
import base64
import hashlib
import secrets
import urllib.parse
from bs4 import BeautifulSoup
import time

class OutlookClient:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
        })
        
        # 固定参数
        self.client_id = "9199bf20-a13f-4107-85dc-02114787ef48"
        self.redirect_uri = "https://outlook.live.com/mail/"
        self.scope = "https://outlook.office.com/.default openid profile offline_access"
        
        # 动态参数
        self.uaid = None
        self.state = None
        self.nonce = None
        self.code_verifier = None
        self.code_challenge = None
        self.ppft_token = None
        self.context_params = {}
        self.auth_token = None
        self.session_id = None
        self.anchor_mailbox = None
        
    def generate_pkce_params(self):
        """生成PKCE参数"""
        self.code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        challenge_bytes = hashlib.sha256(self.code_verifier.encode('utf-8')).digest()
        self.code_challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')
        
    def generate_session_params(self):
        """生成会话参数"""
        self.uaid = secrets.token_hex(16)
        self.nonce = f"{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}"
        
        # 生成state参数
        state_data = {
            "id": f"{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}",
            "meta": {"interactionType": "redirect"}
        }
        self.state = base64.b64encode(json.dumps(state_data).encode()).decode()

    def step1_oauth_authorize(self, username):
        """第1步：OAuth授权流程初始化"""
        print("第1步：OAuth授权流程初始化...")
        
        self.generate_pkce_params()
        self.generate_session_params()
        
        params = {
            'client_id': self.client_id,
            'scope': self.scope,
            'redirect_uri': self.redirect_uri,
            'response_type': 'code',
            'state': self.state,
            'response_mode': 'fragment',
            'nonce': self.nonce,
            'code_challenge': self.code_challenge,
            'code_challenge_method': 'S256',
            'x-client-SKU': 'msal.js.browser',
            'x-client-Ver': '4.14.0',
            'uaid': self.uaid,
            'msproxy': '1',
            'issuer': 'mso',
            'tenant': 'common',
            'ui_locales': 'zh-CN',
            'client_info': '1',
            'username': username,
            'login_hint': username
        }
        
        url = "https://login.live.com/oauth20_authorize.srf"
        response = self.session.get(url, params=params)
        
        if response.status_code != 200:
            raise Exception(f"OAuth授权初始化失败: {response.status_code}")
            
        # 提取PPFT令牌
        soup = BeautifulSoup(response.text, 'html.parser')
        ppft_input = soup.find('input', {'name': 'PPFT'})
        if ppft_input:
            self.ppft_token = ppft_input.get('value')
        
        # 提取ServerData中的参数
        script_match = re.search(r'var ServerData = ({.*?});', response.text, re.DOTALL)
        if script_match:
            try:
                # 简单解析，实际可能需要更复杂的处理
                server_data_str = script_match.group(1)
                # 提取关键参数
                context_match = re.search(r"contextid:'([^']+)'", server_data_str)
                opid_match = re.search(r"opid:'([^']+)'", server_data_str)
                bk_match = re.search(r"bk:(\d+)", server_data_str)
                
                if context_match:
                    self.context_params['contextid'] = context_match.group(1)
                if opid_match:
                    self.context_params['opid'] = opid_match.group(1)
                if bk_match:
                    self.context_params['bk'] = bk_match.group(1)
            except:
                pass
        
        print(f"✓ 获取PPFT令牌: {self.ppft_token[:50]}...")
        return True

    def step2_get_experiments(self):
        """第2步：获取实验配置"""
        print("第2步：获取实验配置...")
        
        url = "https://login.live.com/GetExperimentAssignments.srf"
        headers = {
            'Content-Type': 'application/json; charset=utf-8',
            'correlationId': self.uaid,
            'client-request-id': self.uaid,
            'hpgid': '33',
            'hpgact': '0',
            'Origin': 'https://login.live.com',
            'Referer': 'https://login.live.com/',
        }
        
        data = {
            "clientExperiments": [
                {
                    "parallax": "enableidentitybannerresponsiveexperiment",
                    "control": "enableidentitybannerresponsiveexperiment_control",
                    "treatments": ["enableidentitybannerresponsiveexperiment_treatment"]
                }
            ]
        }
        
        response = self.session.post(url, headers=headers, json=data)
        print(f"✓ 实验配置获取完成: {response.status_code}")
        return True

    def step3_password_auth(self, username, password):
        """第3步：用户名密码验证"""
        print("第3步：用户名密码验证...")
        
        url = "https://login.live.com/ppsecure/post.srf"
        params = {
            'username': username,
            'client_id': self.client_id,
            'uaid': self.uaid,
            'pid': '15216'
        }
        
        # 添加从第1步获取的上下文参数
        params.update(self.context_params)
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://login.live.com',
            'Referer': 'https://login.live.com/',
        }
        
        data = {
            'ps': '2',
            'PPFT': self.ppft_token,
            'PPSX': 'Passport',
            'NewUser': '1',
            'login': username,
            'loginfmt': username,
            'passwd': password,
            'type': '11',
            'LoginOptions': '3',
            'IsFidoSupported': '1',
            'isSignupPost': '0',
            'isRecoveryAttemptPost': '0',
        }
        
        response = self.session.post(url, params=params, headers=headers, data=data)
        
        if response.status_code != 200:
            raise Exception(f"密码验证失败: {response.status_code}")
        
        # 提取新的PPFT令牌
        soup = BeautifulSoup(response.text, 'html.parser')
        ppft_input = soup.find('input', {'name': 'PPFT'})
        if ppft_input:
            self.ppft_token = ppft_input.get('value')
            print(f"✓ 密码验证成功，获取新PPFT: {self.ppft_token[:50]}...")
        else:
            # 检查是否有错误信息
            if "incorrect" in response.text.lower() or "error" in response.text.lower():
                raise Exception("用户名或密码错误")
        
        return True

    def step4_keep_signed_in(self):
        """第4步：保持登录状态确认"""
        print("第4步：保持登录状态确认...")
        
        url = "https://login.live.com/ppsecure/post.srf"
        params = {
            'username': '',  # 这一步通常不需要username
            'client_id': self.client_id,
            'uaid': self.uaid,
            'pid': '15216'
        }
        params.update(self.context_params)
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://login.live.com',
            'Referer': 'https://login.live.com/',
        }
        
        data = {
            'PPFT': self.ppft_token,
            'LoginOptions': '1',
            'type': '28',
        }
        
        response = self.session.post(url, params=params, headers=headers, data=data)
        
        if response.status_code != 200:
            raise Exception(f"保持登录确认失败: {response.status_code}")
        
        # 检查是否有重定向表单
        soup = BeautifulSoup(response.text, 'html.parser')
        form = soup.find('form', {'name': 'fmHF'})
        if form:
            print("✓ 获取重定向表单数据")
            return form
        
        return True

    def step5_save_state(self, form_data):
        """第5步：状态保存和重定向"""
        print("第5步：状态保存和重定向...")
        
        if isinstance(form_data, bool):
            return True
            
        # 提取表单数据
        form_inputs = form_data.find_all('input', {'type': 'hidden'})
        post_data = {}
        for input_elem in form_inputs:
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            if name:
                post_data[name] = value
        
        action_url = form_data.get('action', 'https://login.microsoftonline.com/consumers/savestate')
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://login.live.com',
            'Referer': 'https://login.live.com/',
        }
        
        response = self.session.post(action_url, headers=headers, data=post_data, allow_redirects=False)
        
        if response.status_code in [302, 303]:
            print("✓ 状态保存成功，准备重定向到Outlook")
            return True
        
        return True

    def step6_access_outlook(self):
        """第6步：访问Outlook邮箱"""
        print("第6步：访问Outlook邮箱...")

        url = "https://outlook.live.com/mail/"
        headers = {
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Dest': 'document',
            'Referer': 'https://login.live.com/',
        }

        response = self.session.get(url, headers=headers)

        if response.status_code == 200:
            print("✓ 成功访问Outlook邮箱")
            # 生成会话ID
            self.session_id = f"{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}"
            return True

        raise Exception(f"访问Outlook失败: {response.status_code}")

    def step7_get_startup_data(self):
        """第7步：获取启动数据"""
        print("第7步：获取启动数据...")

        url = "https://outlook.live.com/owa/0/startupdata.ashx"
        params = {
            'app': 'Mail',
            'n': '0'
        }

        # 生成MSAuth令牌（简化版本）
        self.auth_token = f"MSAuth1.0 usertoken=\"{secrets.token_hex(32)}\""
        self.anchor_mailbox = f"PUID:{secrets.token_hex(12).upper()}@{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}"

        headers = {
            'Content-Length': '0',
            'x-req-source': 'Mail',
            'authorization': self.auth_token,
            'x-anchormailbox': self.anchor_mailbox,
            'x-owa-sessionid': self.session_id,
            'x-owa-correlationid': f"accountPolicy_{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}",
            'action': 'StartupData',
            'x-message-count': '25',
            'Origin': 'https://outlook.live.com',
            'Referer': 'https://outlook.live.com/',
        }

        response = self.session.post(url, params=params, headers=headers)

        if response.status_code == 200:
            print("✓ 启动数据获取成功")
            # 这里应该解析响应获取文件夹信息，简化处理
            return True

        raise Exception(f"获取启动数据失败: {response.status_code}")

    def step8_get_folder_digest(self):
        """第8步：获取文件夹变更摘要"""
        print("第8步：获取文件夹变更摘要...")

        url = "https://outlook.live.com/owa/0/service.svc"
        params = {
            'action': 'GetFolderChangeDigest',
            'app': 'Mail',
            'n': '1'
        }

        # 收件箱文件夹ID（示例）
        inbox_folder_id = "AQMkADAwATExADUxNy00YzMzAC1kOWZiLTAwAi0wMAoALgAAAwGz5V/3q8JBoM1efEC//MYBAGGChT0z+8JJrScjqPrv3+MAAAIBDAAAAA=="

        request_data = {
            "__type": "GetFolderChangeDigestRequest:#Exchange",
            "Folders": [{
                "FolderId": {
                    "__type": "TargetFolderId:#Exchange",
                    "BaseFolderId": {
                        "__type": "FolderId:#Exchange",
                        "Id": inbox_folder_id
                    }
                },
                "IsConversationView": False,
                "ViewType": 2,
                "Watermark": time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())
            }],
            "MaxSendersCount": 10
        }

        encoded_data = urllib.parse.quote(json.dumps(request_data))

        headers = {
            'Content-Length': '0',
            'x-req-source': 'Mail',
            'authorization': self.auth_token,
            'action': 'GetFolderChangeDigest',
            'x-anchormailbox': self.anchor_mailbox,
            'x-owa-sessionid': self.session_id,
            'x-owa-urlpostdata': encoded_data,
            'Content-Type': 'application/json; charset=utf-8',
            'Origin': 'https://outlook.live.com',
            'Referer': 'https://outlook.live.com/',
        }

        response = self.session.post(url, params=params, headers=headers)

        if response.status_code == 200:
            print("✓ 文件夹变更摘要获取成功")
            return inbox_folder_id

        raise Exception(f"获取文件夹摘要失败: {response.status_code}")

    def step9_get_folder_info(self, folder_id):
        """第9步：获取文件夹信息"""
        print("第9步：获取文件夹信息...")

        url = "https://outlook.live.com/owa/0/service.svc"
        params = {
            'action': 'GetFolder',
            'app': 'Mail',
            'n': '6'
        }

        request_data = {
            "__type": "GetFolderJsonRequest:#Exchange",
            "Header": {
                "__type": "JsonRequestHeaders:#Exchange",
                "RequestServerVersion": "V2018_01_08",
                "TimeZoneContext": {
                    "__type": "TimeZoneContext:#Exchange",
                    "TimeZoneDefinition": {
                        "__type": "TimeZoneDefinitionType:#Exchange",
                        "Id": "SE Asia Standard Time"
                    }
                }
            },
            "Body": {
                "__type": "GetFolderRequest:#Exchange",
                "FolderShape": {
                    "__type": "FolderResponseShape:#Exchange",
                    "BaseShape": "IdOnly",
                    "AdditionalProperties": [
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "TotalCount"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "UnreadCount"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "ParentFolderId"}
                    ]
                },
                "FolderIds": [{"__type": "FolderId:#Exchange", "Id": folder_id}]
            }
        }

        encoded_data = urllib.parse.quote(json.dumps(request_data))

        headers = {
            'Content-Length': '0',
            'x-req-source': 'Mail',
            'authorization': self.auth_token,
            'action': 'GetFolder',
            'x-anchormailbox': self.anchor_mailbox,
            'x-owa-sessionid': self.session_id,
            'x-owa-urlpostdata': encoded_data,
            'Content-Type': 'application/json; charset=utf-8',
            'Origin': 'https://outlook.live.com',
            'Referer': 'https://outlook.live.com/',
        }

        response = self.session.post(url, params=params, headers=headers)

        if response.status_code == 200:
            print("✓ 文件夹信息获取成功")
            return True

        raise Exception(f"获取文件夹信息失败: {response.status_code}")

    def step10_find_items(self, folder_id, max_entries=25):
        """第10步：查找邮件项目"""
        print("第10步：查找邮件项目...")

        url = "https://outlook.live.com/owa/0/service.svc"
        params = {
            'action': 'FindItem',
            'app': 'Mail',
            'n': '4'
        }

        request_data = {
            "__type": "FindItemJsonRequest:#Exchange",
            "Header": {
                "__type": "JsonRequestHeaders:#Exchange",
                "RequestServerVersion": "V2018_01_08",
                "TimeZoneContext": {
                    "__type": "TimeZoneContext:#Exchange",
                    "TimeZoneDefinition": {
                        "__type": "TimeZoneDefinitionType:#Exchange",
                        "Id": "SE Asia Standard Time"
                    }
                }
            },
            "Body": {
                "ParentFolderIds": [{"__type": "FolderId:#Exchange", "Id": folder_id}],
                "ItemShape": {
                    "__type": "ItemResponseShape:#Exchange",
                    "BaseShape": "IdOnly",
                    "AdditionalProperties": [
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "Subject"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "DateTimeReceived"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "From"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "IsRead"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "HasAttachments"}
                    ]
                },
                "ShapeName": "MailListItem",
                "Paging": {
                    "__type": "IndexedPageView:#Exchange",
                    "BasePoint": "Beginning",
                    "Offset": 0,
                    "MaxEntriesReturned": max_entries
                },
                "ViewFilter": "All",
                "SortOrder": [
                    {
                        "__type": "SortResults:#Exchange",
                        "Order": "Descending",
                        "Path": {"__type": "PropertyUri:#Exchange", "FieldURI": "DateTimeReceived"}
                    }
                ],
                "Traversal": "Shallow"
            }
        }

        encoded_data = urllib.parse.quote(json.dumps(request_data))

        headers = {
            'Content-Length': '0',
            'x-req-source': 'Mail',
            'authorization': self.auth_token,
            'action': 'FindItem',
            'x-anchormailbox': self.anchor_mailbox,
            'x-owa-sessionid': self.session_id,
            'x-owa-urlpostdata': encoded_data,
            'Content-Type': 'application/json; charset=utf-8',
            'Origin': 'https://outlook.live.com',
            'Referer': 'https://outlook.live.com/',
        }

        response = self.session.post(url, params=params, headers=headers)

        if response.status_code == 200:
            print("✓ 邮件列表获取成功")
            try:
                result = response.json()
                return result
            except:
                return response.text

        raise Exception(f"获取邮件列表失败: {response.status_code}")

    def login_and_get_emails(self, username, password, max_emails=25):
        """完整的登录和获取邮件流程"""
        try:
            print(f"开始登录流程，用户: {username}")
            print("=" * 50)

            # 执行完整流程
            self.step1_oauth_authorize(username)
            time.sleep(1)

            self.step2_get_experiments()
            time.sleep(1)

            self.step3_password_auth(username, password)
            time.sleep(1)

            form_data = self.step4_keep_signed_in()
            time.sleep(1)

            self.step5_save_state(form_data)
            time.sleep(1)

            self.step6_access_outlook()
            time.sleep(1)

            self.step7_get_startup_data()
            time.sleep(1)

            folder_id = self.step8_get_folder_digest()
            time.sleep(1)

            self.step9_get_folder_info(folder_id)
            time.sleep(1)

            emails = self.step10_find_items(folder_id, max_emails)

            print("=" * 50)
            print("✅ 登录流程完成！")
            return emails

        except Exception as e:
            print(f"❌ 登录失败: {str(e)}")
            raise

def main():
    """使用示例"""
    # 创建客户端
    client = OutlookClient()

    # 设置登录信息
    username = "<EMAIL>"  # 替换为你的邮箱
    password = "your_password"           # 替换为你的密码

    try:
        # 执行登录和获取邮件
        emails = client.login_and_get_emails(username, password, max_emails=10)

        print("\n📧 邮件列表:")
        print("-" * 50)

        if isinstance(emails, dict):
            # 解析邮件数据
            try:
                items = emails.get('Body', {}).get('ResponseMessages', {}).get('Items', [])
                if items:
                    for item in items:
                        root_folder = item.get('RootFolder', {})
                        mail_items = root_folder.get('Items', [])

                        for mail in mail_items:
                            subject = mail.get('Subject', '无主题')
                            from_addr = mail.get('From', {}).get('Mailbox', {}).get('EmailAddress', '未知发件人')
                            received_time = mail.get('DateTimeReceived', '未知时间')
                            is_read = mail.get('IsRead', False)

                            status = "已读" if is_read else "未读"
                            print(f"📧 {subject}")
                            print(f"   发件人: {from_addr}")
                            print(f"   时间: {received_time}")
                            print(f"   状态: {status}")
                            print("-" * 30)
                else:
                    print("没有找到邮件")
            except Exception as e:
                print(f"解析邮件数据时出错: {e}")
                print("原始响应:", emails)
        else:
            print("响应格式不正确:", emails)

    except Exception as e:
        print(f"程序执行失败: {e}")

if __name__ == "__main__":
    main()
