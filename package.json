{"name": "local-packet-capture", "version": "1.0.0", "description": "本地抓包工具，用于抓取指定域名的HTTPS请求", "main": "capture.js", "scripts": {"start": "node start.js", "basic": "node capture.js", "advanced": "node advanced-capture.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["packet-capture", "https", "proxy"], "author": "", "license": "MIT", "dependencies": {"http-proxy": "^1.18.1", "https": "^1.0.0", "fs": "^0.0.1-security", "path": "^0.12.7"}}