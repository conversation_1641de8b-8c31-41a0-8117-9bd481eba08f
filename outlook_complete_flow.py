#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的Outlook邮箱登录和邮件获取流程
基于成功的PPFT令牌提取实现
"""

import requests
import re
import json
import base64
import hashlib
import secrets
import urllib.parse
from bs4 import BeautifulSoup
import time

class OutlookCompleteFlow:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
        })
        
        # 固定参数
        self.client_id = "9199bf20-a13f-4107-85dc-02114787ef48"
        self.redirect_uri = "https://outlook.live.com/mail/"
        self.scope = "https://outlook.office.com/.default openid profile offline_access"
        
        # 动态参数
        self.uaid = None
        self.ppft_token = None
        self.url_post = None
        self.context_params = {}
        
    def generate_oauth_params(self, username):
        """生成OAuth参数"""
        # 生成会话参数
        self.uaid = secrets.token_hex(16)
        nonce = f"{secrets.token_hex(4)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(6)}"
        
        # 生成PKCE参数
        code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        challenge_bytes = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        code_challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')
        
        # 生成state参数
        state_data = {
            "id": f"{secrets.token_hex(4)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(6)}",
            "meta": {"interactionType": "redirect"}
        }
        state = base64.b64encode(json.dumps(state_data).encode()).decode()
        
        return {
            'client_id': self.client_id,
            'scope': self.scope,
            'redirect_uri': self.redirect_uri,
            'response_type': 'code',
            'state': state,
            'response_mode': 'fragment',
            'nonce': nonce,
            'code_challenge': code_challenge,
            'code_challenge_method': 'S256',
            'uaid': self.uaid,
            'username': username,
            'login_hint': username
        }

    def step1_get_login_page(self, username):
        """第1步：获取登录页面和PPFT令牌"""
        print("第1步：获取登录页面...")
        
        params = self.generate_oauth_params(username)
        url = "https://login.live.com/oauth20_authorize.srf"
        
        headers = {
            'Referer': 'https://login.microsoftonline.com/',
        }
        
        response = self.session.get(url, params=params, headers=headers, timeout=30)
        
        if response.status_code != 200:
            raise Exception(f"获取登录页面失败: {response.status_code}")
        
        print(f"✅ 登录页面获取成功，大小: {len(response.text)} 字符")
        
        # 提取PPFT令牌
        self.extract_login_params(response.text)
        
        if not self.ppft_token:
            raise Exception("未能获取PPFT令牌")
        
        print(f"✅ PPFT令牌: {self.ppft_token[:50]}...")
        print(f"✅ POST URL: {self.url_post[:80]}...")
        
        return True

    def extract_login_params(self, html_content):
        """从HTML中提取登录参数"""
        # 提取PPFT令牌
        # 方法1: 从sFTTag中提取
        sft_match = re.search(r'sFTTag:["\']([^"\']+)["\']', html_content)
        if sft_match:
            sft_content = sft_match.group(1)
            ppft_in_sft = re.search(r'value=["\']([^"\']+)["\']', sft_content)
            if ppft_in_sft:
                self.ppft_token = ppft_in_sft.group(1)
        
        # 方法2: 直接查找input标签
        if not self.ppft_token:
            ppft_input_match = re.search(r'<input[^>]*name=["\']PPFT["\'][^>]*value=["\']([^"\']+)["\']', html_content)
            if ppft_input_match:
                self.ppft_token = ppft_input_match.group(1)
        
        # 提取urlPost
        url_post_match = re.search(r'urlPost:["\']([^"\']+)["\']', html_content)
        if url_post_match:
            self.url_post = url_post_match.group(1)
        
        # 提取上下文参数
        if self.url_post:
            parsed_url = urllib.parse.urlparse(self.url_post)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            for key, values in query_params.items():
                if values:
                    self.context_params[key] = values[0]

    def step2_password_login(self, username, password):
        """第2步：提交用户名密码"""
        print("第2步：提交用户名密码...")
        
        if not self.url_post:
            raise Exception("没有POST URL")
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://login.live.com',
            'Referer': 'https://login.live.com/',
        }
        
        data = {
            'ps': '2',
            'PPFT': self.ppft_token,
            'PPSX': 'Passport',
            'NewUser': '1',
            'login': username,
            'loginfmt': username,
            'passwd': password,
            'type': '11',
            'LoginOptions': '3',
            'IsFidoSupported': '1',
            'isSignupPost': '0',
            'isRecoveryAttemptPost': '0',
        }
        
        response = self.session.post(self.url_post, headers=headers, data=data, timeout=30)
        
        if response.status_code != 200:
            raise Exception(f"密码验证失败: {response.status_code}")
        
        print(f"✅ 密码验证响应: {response.status_code}")
        
        # 检查是否需要保持登录状态
        if 'fmHF' in response.text or 'DoSubmit' in response.text:
            print("✅ 需要处理保持登录状态")
            return self.handle_keep_signed_in(response)
        
        return True

    def handle_keep_signed_in(self, response):
        """处理保持登录状态"""
        print("第3步：处理保持登录状态...")
        
        soup = BeautifulSoup(response.text, 'html.parser')
        form = soup.find('form', {'name': 'fmHF'})
        
        if not form:
            print("⚠️ 未找到重定向表单，可能已经完成登录")
            return True
        
        # 提取表单数据
        action_url = form.get('action')
        form_data = {}
        
        for input_elem in form.find_all('input', {'type': 'hidden'}):
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            if name:
                form_data[name] = value
        
        print(f"✅ 重定向到: {action_url}")
        print(f"✅ 表单参数数量: {len(form_data)}")
        
        # 提交表单
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://login.live.com',
            'Referer': response.url,
        }
        
        redirect_response = self.session.post(action_url, headers=headers, data=form_data, timeout=30)
        
        if redirect_response.status_code in [200, 302, 303]:
            print("✅ 重定向处理成功")
            return True
        
        raise Exception(f"重定向处理失败: {redirect_response.status_code}")

    def step3_access_outlook(self):
        """第4步：访问Outlook邮箱"""
        print("第4步：访问Outlook邮箱...")
        
        url = "https://outlook.live.com/mail/"
        headers = {
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Dest': 'document',
        }
        
        response = self.session.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print("✅ 成功访问Outlook邮箱")
            
            # 检查是否真的进入了邮箱
            if 'loadingScreen' in response.text and 'Outlook' in response.text:
                print("✅ 确认进入Outlook主页")
                return True
            else:
                print("⚠️ 可能还在登录流程中")
                return False
        
        raise Exception(f"访问Outlook失败: {response.status_code}")

    def complete_login_flow(self, username, password):
        """完整的登录流程"""
        try:
            print(f"开始Outlook登录流程，用户: {username}")
            print("=" * 50)
            
            # 第1步：获取登录页面
            self.step1_get_login_page(username)
            time.sleep(1)
            
            # 第2步：密码验证
            self.step2_password_login(username, password)
            time.sleep(1)
            
            # 第3步：访问Outlook
            success = self.step3_access_outlook()
            
            if success:
                print("=" * 50)
                print("🎉 登录成功！已进入Outlook邮箱")
                return True
            else:
                print("⚠️ 登录可能未完全成功")
                return False
                
        except Exception as e:
            print(f"❌ 登录失败: {str(e)}")
            raise

def main():
    """主函数"""
    print("Outlook完整登录流程测试")
    print("=" * 50)
    
    # 设置登录信息
    username = input("请输入邮箱地址: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return
    
    client = OutlookCompleteFlow()
    
    try:
        success = client.complete_login_flow(username, password)
        
        if success:
            print("\n✅ 登录流程完成！")
            print("现在可以继续实现邮件获取功能")
        else:
            print("\n⚠️ 登录流程可能需要进一步处理")
            
    except Exception as e:
        print(f"\n❌ 登录失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
