#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的Outlook邮箱登录和邮件获取流程
基于成功的PPFT令牌提取实现
"""

import requests
import re
import json
import base64
import hashlib
import secrets
import urllib.parse
from bs4 import BeautifulSoup
import time

class OutlookCompleteFlow:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
        })
        
        # 固定参数
        self.client_id = "9199bf20-a13f-4107-85dc-02114787ef48"
        self.redirect_uri = "https://outlook.live.com/mail/"
        self.scope = "https://outlook.office.com/.default openid profile offline_access"
        
        # 动态参数
        self.uaid = None
        self.ppft_token = None
        self.url_post = None
        self.context_params = {}
        
    def generate_oauth_params(self, username):
        """生成OAuth参数"""
        # 生成会话参数
        self.uaid = secrets.token_hex(16)
        nonce = f"{secrets.token_hex(4)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(6)}"
        
        # 生成PKCE参数
        code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        challenge_bytes = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        code_challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')
        
        # 生成state参数
        state_data = {
            "id": f"{secrets.token_hex(4)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(6)}",
            "meta": {"interactionType": "redirect"}
        }
        state = base64.b64encode(json.dumps(state_data).encode()).decode()
        
        return {
            'client_id': self.client_id,
            'scope': self.scope,
            'redirect_uri': self.redirect_uri,
            'response_type': 'code',
            'state': state,
            'response_mode': 'fragment',
            'nonce': nonce,
            'code_challenge': code_challenge,
            'code_challenge_method': 'S256',
            'uaid': self.uaid,
            'username': username,
            'login_hint': username
        }

    def step1_get_login_page(self, username):
        """第1步：获取登录页面和PPFT令牌"""
        print("第1步：获取登录页面...")
        
        params = self.generate_oauth_params(username)
        url = "https://login.live.com/oauth20_authorize.srf"
        
        headers = {
            'Referer': 'https://login.microsoftonline.com/',
        }
        
        response = self.session.get(url, params=params, headers=headers, timeout=30)
        
        if response.status_code != 200:
            raise Exception(f"获取登录页面失败: {response.status_code}")
        
        print(f"✅ 登录页面获取成功，大小: {len(response.text)} 字符")
        
        # 提取PPFT令牌
        self.extract_login_params(response.text)
        
        if not self.ppft_token:
            raise Exception("未能获取PPFT令牌")
        
        print(f"✅ PPFT令牌: {self.ppft_token[:50]}...")
        print(f"✅ POST URL: {self.url_post[:80]}...")
        
        return True

    def extract_login_params(self, html_content):
        """从HTML中提取登录参数"""
        # 提取PPFT令牌
        # 方法1: 从sFTTag中提取
        sft_match = re.search(r'sFTTag:["\']([^"\']+)["\']', html_content)
        if sft_match:
            sft_content = sft_match.group(1)
            ppft_in_sft = re.search(r'value=["\']([^"\']+)["\']', sft_content)
            if ppft_in_sft:
                self.ppft_token = ppft_in_sft.group(1)
        
        # 方法2: 直接查找input标签
        if not self.ppft_token:
            ppft_input_match = re.search(r'<input[^>]*name=["\']PPFT["\'][^>]*value=["\']([^"\']+)["\']', html_content)
            if ppft_input_match:
                self.ppft_token = ppft_input_match.group(1)
        
        # 提取urlPost
        url_post_match = re.search(r'urlPost:["\']([^"\']+)["\']', html_content)
        if url_post_match:
            self.url_post = url_post_match.group(1)
        
        # 提取上下文参数
        if self.url_post:
            parsed_url = urllib.parse.urlparse(self.url_post)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            for key, values in query_params.items():
                if values:
                    self.context_params[key] = values[0]

    def step2_password_login(self, username, password):
        """第2步：提交用户名密码"""
        print("第2步：提交用户名密码...")
        
        if not self.url_post:
            raise Exception("没有POST URL")
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://login.live.com',
            'Referer': 'https://login.live.com/',
        }
        
        data = {
            'ps': '2',
            'PPFT': self.ppft_token,
            'PPSX': 'Passport',
            'NewUser': '1',
            'login': username,
            'loginfmt': username,
            'passwd': password,
            'type': '11',
            'LoginOptions': '3',
            'IsFidoSupported': '1',
            'isSignupPost': '0',
            'isRecoveryAttemptPost': '0',
        }
        
        response = self.session.post(self.url_post, headers=headers, data=data, timeout=30)
        
        if response.status_code != 200:
            raise Exception(f"密码验证失败: {response.status_code}")
        
        print(f"✅ 密码验证响应: {response.status_code}")
        
        # 检查是否需要保持登录状态
        if 'fmHF' in response.text or 'DoSubmit' in response.text:
            print("✅ 需要处理保持登录状态")
            return self.handle_keep_signed_in(response)
        
        return True

    def handle_keep_signed_in(self, response):
        """处理保持登录状态"""
        print("第3步：处理保持登录状态...")
        
        soup = BeautifulSoup(response.text, 'html.parser')
        form = soup.find('form', {'name': 'fmHF'})
        
        if not form:
            print("⚠️ 未找到重定向表单，可能已经完成登录")
            return True
        
        # 提取表单数据
        action_url = form.get('action')
        form_data = {}
        
        for input_elem in form.find_all('input', {'type': 'hidden'}):
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            if name:
                form_data[name] = value
        
        print(f"✅ 重定向到: {action_url}")
        print(f"✅ 表单参数数量: {len(form_data)}")
        
        # 提交表单
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://login.live.com',
            'Referer': response.url,
        }
        
        redirect_response = self.session.post(action_url, headers=headers, data=form_data, timeout=30)
        
        if redirect_response.status_code in [200, 302, 303]:
            print("✅ 重定向处理成功")
            return True
        
        raise Exception(f"重定向处理失败: {redirect_response.status_code}")

    def step3_access_outlook(self):
        """第4步：访问Outlook邮箱"""
        print("第4步：访问Outlook邮箱...")

        url = "https://outlook.live.com/mail/"
        headers = {
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Dest': 'document',
        }

        response = self.session.get(url, headers=headers, timeout=30)

        if response.status_code == 200:
            print("✅ 成功访问Outlook邮箱")

            # 检查是否真的进入了邮箱
            if 'loadingScreen' in response.text and 'Outlook' in response.text:
                print("✅ 确认进入Outlook主页")

                # 尝试从页面中提取认证信息
                self.extract_auth_info(response.text)
                return True
            else:
                print("⚠️ 可能还在登录流程中")
                return False

        raise Exception(f"访问Outlook失败: {response.status_code}")

    def extract_auth_info(self, html_content):
        """从Outlook主页提取认证信息"""
        print("🔍 提取认证信息...")

        # 查找ClientId Cookie（这通常包含用户标识信息）
        for cookie in self.session.cookies:
            if cookie.name == 'ClientId':
                print(f"✅ 找到ClientId: {cookie.value[:30]}...")
                # ClientId通常包含PUID信息
                break

        # 从HTML中查找其他可能的认证信息
        # 有时候页面会包含用户信息的JavaScript变量
        user_info_match = re.search(r'userPuid["\']?\s*[:=]\s*["\']([^"\']+)["\']', html_content, re.IGNORECASE)
        if user_info_match:
            puid = user_info_match.group(1)
            print(f"✅ 找到PUID: {puid}")

        # 查找其他认证相关信息
        auth_match = re.search(r'authToken["\']?\s*[:=]\s*["\']([^"\']+)["\']', html_content, re.IGNORECASE)
        if auth_match:
            auth_token = auth_match.group(1)
            print(f"✅ 找到认证令牌: {auth_token[:30]}...")

        print("✅ 认证信息提取完成")

    def step4_get_startup_data(self):
        """第5步：获取启动数据"""
        print("第5步：获取启动数据...")

        # 首先尝试简化的方法，不需要复杂的认证
        # 因为我们已经通过Cookie认证了
        url = "https://outlook.live.com/owa/0/startupdata.ashx"
        params = {
            'app': 'Mail',
            'n': '0'
        }

        # 生成会话ID
        session_id = f"{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}"
        correlation_id = f"accountPolicy_{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}"

        # 使用更简化的头部，依赖Cookie认证
        headers = {
            'Content-Length': '0',
            'x-req-source': 'Mail',
            'x-owa-sessionid': session_id,
            'x-owa-correlationid': correlation_id,
            'action': 'StartupData',
            'x-message-count': '25',
            'Accept': '*/*',
            'Origin': 'https://outlook.live.com',
            'Referer': 'https://outlook.live.com/',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
        }

        print(f"🔄 请求启动数据，会话ID: {session_id}")

        response = self.session.post(url, params=params, headers=headers, timeout=30)

        print(f"📊 启动数据响应: {response.status_code}")

        if response.status_code == 200:
            print("✅ 启动数据获取成功")
            try:
                startup_data = response.json()
                # 保存会话信息
                self.session_id = session_id
                print(f"✅ 启动数据解析成功，数据大小: {len(str(startup_data))} 字符")
                return startup_data
            except:
                print("⚠️ 启动数据解析失败，但请求成功")
                print(f"响应内容: {response.text[:200]}...")
                return True
        elif response.status_code == 440:
            print("⚠️ 收到440错误，可能需要重新认证或使用不同的方法")
            print("尝试跳过启动数据步骤，直接获取邮件...")
            return True

        raise Exception(f"获取启动数据失败: {response.status_code}")

    def step5_find_emails(self, max_emails=10):
        """第6步：查找邮件（简化版本）"""
        print(f"第6步：尝试获取邮件信息...")

        # 方法1: 尝试访问邮件API
        try:
            return self.try_find_emails_api(max_emails)
        except Exception as e:
            print(f"⚠️ API方法失败: {e}")

        # 方法2: 尝试访问邮件页面并解析
        try:
            return self.try_parse_mail_page()
        except Exception as e:
            print(f"⚠️ 页面解析方法失败: {e}")

        # 如果都失败了，返回空列表
        print("⚠️ 所有邮件获取方法都失败了，但登录是成功的")
        return []

    def try_find_emails_api(self, max_emails=10):
        """尝试使用API获取邮件"""
        url = "https://outlook.live.com/owa/0/service.svc"
        params = {
            'action': 'FindItem',
            'app': 'Mail',
            'n': '1'
        }

        # 简化的请求数据
        request_data = {
            "__type": "FindItemJsonRequest:#Exchange",
            "Header": {
                "__type": "JsonRequestHeaders:#Exchange",
                "RequestServerVersion": "V2018_01_08"
            },
            "Body": {
                "ParentFolderIds": [{"__type": "DistinguishedFolderId:#Exchange", "Id": "inbox"}],
                "ItemShape": {
                    "__type": "ItemResponseShape:#Exchange",
                    "BaseShape": "IdOnly",
                    "AdditionalProperties": [
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "Subject"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "DateTimeReceived"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "From"}
                    ]
                },
                "Paging": {
                    "__type": "IndexedPageView:#Exchange",
                    "BasePoint": "Beginning",
                    "Offset": 0,
                    "MaxEntriesReturned": max_emails
                },
                "SortOrder": [
                    {
                        "__type": "SortResults:#Exchange",
                        "Order": "Descending",
                        "Path": {"__type": "PropertyUri:#Exchange", "FieldURI": "DateTimeReceived"}
                    }
                ]
            }
        }

        encoded_data = urllib.parse.quote(json.dumps(request_data))

        headers = {
            'Content-Length': '0',
            'x-req-source': 'Mail',
            'action': 'FindItem',
            'x-owa-sessionid': getattr(self, 'session_id', f"{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}"),
            'x-owa-urlpostdata': encoded_data,
            'Accept': '*/*',
            'Origin': 'https://outlook.live.com',
            'Referer': 'https://outlook.live.com/',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
        }

        response = self.session.post(url, params=params, headers=headers, timeout=30)

        if response.status_code == 200:
            print("✅ 邮件API查询成功")
            try:
                emails_data = response.json()
                return emails_data
            except:
                print("⚠️ 邮件数据解析失败")
                return response.text

        raise Exception(f"邮件API查询失败: {response.status_code}")

    def try_parse_mail_page(self):
        """尝试从邮件页面解析邮件信息"""
        print("🔄 尝试从页面解析邮件信息...")

        # 重新访问邮件页面，看看是否有邮件信息
        url = "https://outlook.live.com/mail/"
        response = self.session.get(url, timeout=30)

        if response.status_code == 200:
            # 这里可以尝试从页面中解析邮件信息
            # 但由于现代Outlook是JavaScript驱动的，可能需要更复杂的方法
            print("✅ 邮件页面访问成功")

            # 简单检查页面是否包含邮件相关内容
            if 'inbox' in response.text.lower() or 'message' in response.text.lower():
                print("✅ 页面包含邮件相关内容")
                return [{"subject": "页面解析方法需要进一步开发", "from": "系统提示", "received_time": "现在"}]

        return []

    def parse_emails(self, emails_data):
        """解析邮件数据"""
        print("解析邮件数据...")

        emails = []

        try:
            if isinstance(emails_data, dict):
                # 查找邮件项目
                body = emails_data.get('Body', {})
                response_messages = body.get('ResponseMessages', {})
                items = response_messages.get('Items', [])

                if items:
                    root_folder = items[0].get('RootFolder', {})
                    mail_items = root_folder.get('Items', [])

                    for mail in mail_items:
                        email_info = {
                            'subject': mail.get('Subject', '无主题'),
                            'from': self.extract_from_info(mail.get('From', {})),
                            'received_time': mail.get('DateTimeReceived', '未知时间'),
                            'is_read': mail.get('IsRead', False),
                            'has_attachments': mail.get('HasAttachments', False),
                            'size': mail.get('Size', 0)
                        }
                        emails.append(email_info)

                    print(f"✅ 成功解析 {len(emails)} 封邮件")
                else:
                    print("📧 收件箱为空")
            else:
                print("❌ 邮件数据格式异常")

        except Exception as e:
            print(f"❌ 解析邮件时出错: {e}")

        return emails

    def extract_from_info(self, from_data):
        """提取发件人信息"""
        if isinstance(from_data, dict):
            mailbox = from_data.get('Mailbox', {})
            name = mailbox.get('Name', '')
            email = mailbox.get('EmailAddress', '')

            if name and email:
                return f"{name} <{email}>"
            elif email:
                return email
            elif name:
                return name

        return "未知发件人"

    def complete_login_flow(self, username, password):
        """完整的登录流程"""
        try:
            print(f"开始Outlook登录流程，用户: {username}")
            print("=" * 50)
            
            # 第1步：获取登录页面
            self.step1_get_login_page(username)
            time.sleep(1)
            
            # 第2步：密码验证
            self.step2_password_login(username, password)
            time.sleep(1)
            
            # 第3步：访问Outlook
            success = self.step3_access_outlook()

            if success:
                print("=" * 50)
                print("🎉 登录成功！已进入Outlook邮箱")
                return True
            else:
                print("⚠️ 登录可能未完全成功")
                return False
                
        except Exception as e:
            print(f"❌ 登录失败: {str(e)}")
            raise

    def login_and_get_emails(self, username, password, max_emails=10):
        """完整的登录和获取邮件流程"""
        try:
            print(f"开始完整流程：登录并获取邮件")
            print("用户:", username)
            print("邮件数量:", max_emails)
            print("=" * 50)

            # 执行登录流程
            success = self.complete_login_flow(username, password)

            if not success:
                raise Exception("登录失败")

            print("\n继续获取邮件...")
            time.sleep(2)

            # 第4步：获取启动数据
            self.step4_get_startup_data()
            time.sleep(1)

            # 第5步：查找邮件
            emails_data = self.step5_find_emails(max_emails)

            # 解析邮件
            emails = self.parse_emails(emails_data)

            print("=" * 50)
            print("📧 邮件获取完成！")

            return emails

        except Exception as e:
            print(f"❌ 完整流程失败: {str(e)}")
            raise

def main():
    """主函数"""
    print("Outlook完整登录和邮件获取工具")
    print("=" * 50)
    print("1. 仅测试登录")
    print("2. 登录并获取邮件")
    print("3. 退出")

    choice = input("\n请选择操作 (1-3): ").strip()

    if choice == '3':
        print("退出程序")
        return

    # 设置登录信息
    username = input("请输入邮箱地址: ").strip()
    password = input("请输入密码: ").strip()

    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return

    client = OutlookCompleteFlow()

    try:
        if choice == '1':
            # 仅测试登录
            success = client.complete_login_flow(username, password)

            if success:
                print("\n✅ 登录流程完成！")
            else:
                print("\n⚠️ 登录流程可能需要进一步处理")

        elif choice == '2':
            # 登录并获取邮件
            max_emails = input("请输入要获取的邮件数量 (默认10): ").strip()
            try:
                max_emails = int(max_emails) if max_emails else 10
            except:
                max_emails = 10

            emails = client.login_and_get_emails(username, password, max_emails)

            # 显示邮件列表
            if emails:
                print(f"\n📧 邮件列表 (共 {len(emails)} 封):")
                print("-" * 80)

                for i, email in enumerate(emails, 1):
                    status = "已读" if email['is_read'] else "未读"
                    attachment = " 📎" if email['has_attachments'] else ""
                    size_kb = email['size'] // 1024 if email['size'] > 0 else 0

                    print(f"{i:2d}. {email['subject']}{attachment}")
                    print(f"     发件人: {email['from']}")
                    print(f"     时间: {email['received_time']}")
                    print(f"     状态: {status} | 大小: {size_kb}KB")
                    print("-" * 80)
            else:
                print("\n📧 没有找到邮件")
        else:
            print("❌ 无效选择")

    except Exception as e:
        print(f"\n❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
