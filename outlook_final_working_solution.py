#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Outlook邮箱的最终工作解决方案
基于抓包数据分析的真实认证流程
"""

import requests
import re
import json
import urllib.parse
import secrets
import time
from outlook_final_solution import OutlookFinalSolution

class OutlookFinalWorkingSolution:
    def __init__(self, logged_in_session):
        """
        使用已登录的会话初始化
        
        Args:
            logged_in_session: 已登录的OutlookFinalSolution实例
        """
        self.session = logged_in_session.session
        self.session_cookies = logged_in_session.session_cookies
        
        # 从抓包数据中提取的固定信息
        self.tenant_id = "84df9e7f-e9f6-40af-b435-aaaaaaaaaaaa"
        self.anchor_mailbox = f"PUID:000115174C33D9FB@{self.tenant_id}"
        
        # 生成会话参数
        self.session_id = f"{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}"
        self.correlation_id_base = f"wHrfj1H2JBO/9LtoC20DwY"
        
        # 邮件相关
        self.folders = {}
        self.inbox_folder_id = None

    def extract_auth_from_outlook_page(self):
        """从Outlook页面提取真实的认证信息"""
        print("🔍 从Outlook页面提取认证信息...")
        
        # 访问Outlook主页，这会触发认证令牌的生成
        url = "https://outlook.live.com/mail/"
        
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Dest': 'document',
        }
        
        response = self.session.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print("✅ 成功访问Outlook页面")
            
            # 现在尝试一个简单的API调用来测试认证
            return self.test_simple_api_call()
        else:
            print(f"❌ 访问Outlook页面失败: {response.status_code}")
            return False

    def test_simple_api_call(self):
        """测试简单的API调用，不需要MSAuth令牌"""
        print("🧪 测试简单的API调用...")
        
        # 尝试GetTimeZone，这是一个相对简单的API
        url = "https://outlook.live.com/owa/0/service.svc"
        params = {
            'action': 'GetTimeZone',
            'app': 'Mail',
            'n': '1'
        }
        
        headers = {
            'content-length': '0',
            'x-req-source': 'Mail',
            'x-owa-sessionid': self.session_id,
            'accept': '*/*',
            'origin': 'https://outlook.live.com',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        }
        
        try:
            response = self.session.post(url, params=params, headers=headers, timeout=30)
            
            print(f"📊 API测试响应: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ API调用成功！Cookie认证有效")
                return True
            elif response.status_code == 401:
                print("❌ 401未授权 - Cookie认证失败")
                return False
            elif response.status_code == 440:
                print("⚠️ 440会话超时 - 需要重新登录")
                return False
            else:
                print(f"⚠️ 未知响应: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ API调用异常: {e}")
            return False

    def get_folders_simple(self):
        """获取文件夹列表（简化版本）"""
        print("📁 获取文件夹列表...")
        
        # 尝试使用最简单的方法获取文件夹
        url = "https://outlook.live.com/owa/0/service.svc"
        params = {
            'action': 'GetFolder',
            'app': 'Mail',
            'n': '1'
        }
        
        headers = {
            'content-length': '0',
            'x-req-source': 'Mail',
            'x-owa-sessionid': self.session_id,
            'accept': '*/*',
            'origin': 'https://outlook.live.com',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        }
        
        try:
            response = self.session.post(url, params=params, headers=headers, timeout=30)
            
            print(f"📊 文件夹响应: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 文件夹请求成功")
                try:
                    data = response.json()
                    print(f"✅ 文件夹数据解析成功: {len(str(data))} 字符")
                    return data
                except:
                    print("⚠️ 文件夹数据解析失败")
                    print(f"响应内容: {response.text[:200]}...")
                    return None
            else:
                print(f"❌ 文件夹请求失败: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                return None
                
        except Exception as e:
            print(f"❌ 文件夹请求异常: {e}")
            return None

    def get_emails_simple(self, max_emails=10):
        """获取邮件列表（最简化版本）"""
        print(f"📧 尝试获取邮件列表...")
        
        # 使用最基本的方法，依赖Cookie认证
        url = "https://outlook.live.com/owa/0/service.svc"
        params = {
            'action': 'FindItem',
            'app': 'Mail',
            'n': '1'
        }
        
        # 构建最简单的FindItem请求
        request_data = {
            "__type": "FindItemJsonRequest:#Exchange",
            "Header": {
                "__type": "JsonRequestHeaders:#Exchange",
                "RequestServerVersion": "V2018_01_08"
            },
            "Body": {
                "ParentFolderIds": [{"__type": "DistinguishedFolderId:#Exchange", "Id": "inbox"}],
                "ItemShape": {
                    "__type": "ItemResponseShape:#Exchange",
                    "BaseShape": "IdOnly",
                    "AdditionalProperties": [
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "Subject"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "DateTimeReceived"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "From"}
                    ]
                },
                "Paging": {
                    "__type": "IndexedPageView:#Exchange",
                    "BasePoint": "Beginning",
                    "Offset": 0,
                    "MaxEntriesReturned": max_emails
                }
            }
        }
        
        encoded_data = urllib.parse.quote(json.dumps(request_data))
        
        headers = {
            'content-length': '0',
            'x-req-source': 'Mail',
            'x-owa-sessionid': self.session_id,
            'x-owa-urlpostdata': encoded_data,
            'accept': '*/*',
            'origin': 'https://outlook.live.com',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        }
        
        try:
            response = self.session.post(url, params=params, headers=headers, timeout=30)
            
            print(f"📊 邮件响应: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 邮件请求成功")
                try:
                    data = response.json()
                    emails = self.parse_emails_simple(data)
                    return emails
                except:
                    print("⚠️ 邮件数据解析失败")
                    print(f"响应内容: {response.text[:200]}...")
                    return []
            else:
                print(f"❌ 邮件请求失败: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                return []
                
        except Exception as e:
            print(f"❌ 邮件请求异常: {e}")
            return []

    def parse_emails_simple(self, data):
        """简单解析邮件数据"""
        emails = []
        
        try:
            if isinstance(data, dict):
                body = data.get('Body', {})
                response_messages = body.get('ResponseMessages', {})
                items = response_messages.get('Items', [])
                
                if items:
                    root_folder = items[0].get('RootFolder', {})
                    mail_items = root_folder.get('Items', [])
                    
                    for mail in mail_items:
                        email_info = {
                            'subject': mail.get('Subject', '无主题'),
                            'from': self.extract_from_simple(mail.get('From', {})),
                            'received_time': mail.get('DateTimeReceived', '未知时间'),
                        }
                        emails.append(email_info)
                    
                    print(f"✅ 成功解析 {len(emails)} 封邮件")
                else:
                    print("📧 收件箱为空")
        except Exception as e:
            print(f"❌ 解析邮件时出错: {e}")
        
        return emails

    def extract_from_simple(self, from_data):
        """简单提取发件人信息"""
        if isinstance(from_data, dict):
            mailbox = from_data.get('Mailbox', {})
            name = mailbox.get('Name', '')
            email = mailbox.get('EmailAddress', '')
            
            if name and email and name != email:
                return f"{name} <{email}>"
            elif email:
                return email
            elif name:
                return name
        
        return "未知发件人"

    def complete_flow(self, max_emails=10):
        """完整的邮件获取流程"""
        try:
            print("开始基于Cookie认证的邮件获取流程")
            print("=" * 50)
            
            # 第1步：从Outlook页面提取认证信息
            if not self.extract_auth_from_outlook_page():
                print("❌ 认证提取失败")
                return []
            
            print("\n第2步：尝试获取文件夹...")
            folders_data = self.get_folders_simple()
            if folders_data:
                print("✅ 文件夹获取成功")
            
            print(f"\n第3步：尝试获取邮件...")
            emails = self.get_emails_simple(max_emails)
            
            print("=" * 50)
            print(f"📧 邮件获取完成！共获取 {len(emails)} 封邮件")
            
            return emails
            
        except Exception as e:
            print(f"❌ 完整流程失败: {str(e)}")
            return []

def main():
    """主函数"""
    print("Outlook邮箱最终工作解决方案")
    print("基于Cookie认证，无需MSAuth令牌")
    print("=" * 50)
    
    # 设置登录信息
    username = input("请输入邮箱地址: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return
    
    # 首先登录
    login_client = OutlookFinalSolution()
    
    try:
        success = login_client.complete_login_flow(username, password)
        
        if success:
            print("\n" + "=" * 50)
            print("🎉 登录成功！开始获取邮件...")
            
            # 创建邮件获取器
            email_client = OutlookFinalWorkingSolution(login_client)
            
            # 获取邮件数量
            max_emails = input("请输入要获取的邮件数量 (默认10): ").strip()
            try:
                max_emails = int(max_emails) if max_emails else 10
            except:
                max_emails = 10
            
            # 执行完整流程
            emails = email_client.complete_flow(max_emails)
            
            # 显示结果
            if emails:
                print(f"\n📧 邮件列表 (共 {len(emails)} 封):")
                print("=" * 80)
                
                for i, email in enumerate(emails, 1):
                    print(f"{i:2d}. {email['subject']}")
                    print(f"     发件人: {email['from']}")
                    print(f"     时间: {email['received_time']}")
                    print("-" * 80)
            else:
                print("\n📧 没有获取到邮件")
                print("💡 可能的原因:")
                print("   - 收件箱为空")
                print("   - 需要更复杂的认证机制")
                print("   - API格式需要进一步调整")
        else:
            print("\n❌ 登录失败")
            
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
