#!/usr/bin/env python3
import re

# 测试PPFT提取
def test_ppft_extraction():
    # 从保存的响应文件中读取内容
    try:
        with open('simple_test_response.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"文件大小: {len(content)} 字符")
        
        # 方法1: 从sFTTag中提取
        sft_match = re.search(r'sFTTag:["\']([^"\']+)["\']', content)
        if sft_match:
            sft_content = sft_match.group(1)
            print(f"找到sFTTag: {len(sft_content)} 字符")
            print(f"sFTTag内容: {sft_content[:100]}...")
            
            ppft_in_sft = re.search(r'value=["\']([^"\']+)["\']', sft_content)
            if ppft_in_sft:
                ppft_value = ppft_in_sft.group(1)
                print(f"✅ 从sFTTag提取PPFT成功: {ppft_value[:50]}...")
                print(f"完整PPFT长度: {len(ppft_value)}")
            else:
                print("❌ 无法从sFTTag中提取PPFT")
        else:
            print("❌ 未找到sFTTag")
        
        # 方法2: 查找urlPost
        url_post_match = re.search(r'urlPost:["\']([^"\']+)["\']', content)
        if url_post_match:
            url_post = url_post_match.group(1)
            print(f"✅ 找到urlPost: {url_post}")
        else:
            print("❌ 未找到urlPost")
        
        # 方法3: 查找其他关键参数
        uaid_match = re.search(r'uaid=([^&"\']+)', content)
        if uaid_match:
            uaid = uaid_match.group(1)
            print(f"✅ 找到uaid: {uaid}")
        
        contextid_match = re.search(r'contextid=([^&"\']+)', content)
        if contextid_match:
            contextid = contextid_match.group(1)
            print(f"✅ 找到contextid: {contextid}")
        
        opid_match = re.search(r'opid=([^&"\']+)', content)
        if opid_match:
            opid = opid_match.group(1)
            print(f"✅ 找到opid: {opid}")
            
    except FileNotFoundError:
        print("❌ 未找到响应文件 simple_test_response.html")
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")

if __name__ == "__main__":
    test_ppft_extraction()
