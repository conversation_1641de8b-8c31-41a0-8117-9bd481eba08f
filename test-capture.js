const http = require('http');
const fs = require('fs');
const path = require('path');

// 测试抓包工具是否正常工作
console.log('🧪 测试抓包工具...');

// 检查文件是否存在
const files = ['capture.js', 'advanced-capture.js', 'start.js'];
console.log('\n📁 检查文件:');
files.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file} - 存在`);
    } else {
        console.log(`❌ ${file} - 不存在`);
    }
});

// 检查保存目录
const saveDir = path.join(__dirname, 'captured_data');
console.log('\n📂 检查保存目录:');
if (fs.existsSync(saveDir)) {
    console.log(`✅ ${saveDir} - 存在`);
    const files = fs.readdirSync(saveDir);
    console.log(`📄 目录中有 ${files.length} 个文件`);
    if (files.length > 0) {
        console.log('最近的文件:');
        files.slice(-3).forEach(file => {
            console.log(`   - ${file}`);
        });
    }
} else {
    console.log(`❌ ${saveDir} - 不存在`);
}

// 测试端口是否可用
console.log('\n🔌 测试端口 8888:');
const testServer = http.createServer();
testServer.listen(8888, () => {
    console.log('✅ 端口 8888 可用');
    testServer.close();
    
    console.log('\n🎉 测试完成！');
    console.log('\n📋 使用说明:');
    console.log('1. 运行 npm start 启动抓包工具');
    console.log('2. 配置浏览器代理为 127.0.0.1:8888');
    console.log('3. 访问目标网站进行抓包');
    console.log('4. 查看 captured_data 目录中的抓包文件');
}).on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log('⚠️  端口 8888 已被占用，请先关闭占用该端口的程序');
    } else {
        console.log(`❌ 端口测试失败: ${err.message}`);
    }
});
