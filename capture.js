const http = require('http');
const https = require('https');
const net = require('net');
const url = require('url');
const fs = require('fs');
const path = require('path');

// 目标域名列表
const TARGET_DOMAINS = [
    'login.live.com',
    'outlook.live.com',
    'account.live.com'
];

// 创建保存目录
const SAVE_DIR = path.join(__dirname, 'captured_data');
if (!fs.existsSync(SAVE_DIR)) {
    fs.mkdirSync(SAVE_DIR, { recursive: true });
}

// 生成文件名
function generateFileName(domain, timestamp) {
    const date = new Date(timestamp);
    const dateStr = date.toISOString().replace(/[:.]/g, '-');
    return `${domain}_${dateStr}.txt`;
}

// 保存数据到文件
function saveToFile(domain, requestData, responseData) {
    const timestamp = Date.now();
    const fileName = generateFileName(domain, timestamp);
    const filePath = path.join(SAVE_DIR, fileName);
    
    const content = `=== 抓包时间: ${new Date(timestamp).toLocaleString()} ===\n` +
                   `=== 域名: ${domain} ===\n\n` +
                   `=== 请求数据 ===\n${requestData}\n\n` +
                   `=== 响应数据 ===\n${responseData}\n\n` +
                   `=== 结束 ===\n`;
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`数据已保存到: ${filePath}`);
}

// 检查是否为目标域名
function isTargetDomain(hostname) {
    return TARGET_DOMAINS.some(domain => hostname.includes(domain));
}

// 创建HTTP代理服务器
const server = http.createServer((req, res) => {
    console.log(`HTTP请求: ${req.method} ${req.url}`);
    
    const urlParts = url.parse(req.url);
    const hostname = urlParts.hostname;
    
    if (isTargetDomain(hostname)) {
        console.log(`捕获到目标域名请求: ${hostname}`);
        
        // 收集请求数据
        let requestBody = '';
        req.on('data', chunk => {
            requestBody += chunk.toString();
        });
        
        req.on('end', () => {
            const requestData = `${req.method} ${req.url} HTTP/${req.httpVersion}\n` +
                              Object.keys(req.headers).map(key => `${key}: ${req.headers[key]}`).join('\n') +
                              `\n\n${requestBody}`;
            
            // 转发请求到目标服务器
            const options = {
                hostname: urlParts.hostname,
                port: urlParts.port || 80,
                path: urlParts.path,
                method: req.method,
                headers: req.headers
            };
            
            const proxyReq = http.request(options, (proxyRes) => {
                let responseBody = '';
                
                proxyRes.on('data', chunk => {
                    responseBody += chunk.toString();
                    res.write(chunk);
                });
                
                proxyRes.on('end', () => {
                    const responseData = `HTTP/${proxyRes.httpVersion} ${proxyRes.statusCode} ${proxyRes.statusMessage}\n` +
                                        Object.keys(proxyRes.headers).map(key => `${key}: ${proxyRes.headers[key]}`).join('\n') +
                                        `\n\n${responseBody}`;
                    
                    // 保存数据
                    saveToFile(hostname, requestData, responseData);
                    res.end();
                });
            });
            
            proxyReq.on('error', (err) => {
                console.error('代理请求错误:', err);
                res.writeHead(500);
                res.end('代理错误');
            });
            
            if (requestBody) {
                proxyReq.write(requestBody);
            }
            proxyReq.end();
        });
    } else {
        // 非目标域名，直接转发
        res.writeHead(200);
        res.end('非目标域名，已忽略');
    }
});

// 处理HTTPS CONNECT请求
server.on('connect', (req, clientSocket, head) => {
    const { hostname, port } = url.parse(`http://${req.url}`);
    
    console.log(`HTTPS CONNECT请求: ${hostname}:${port}`);
    
    if (isTargetDomain(hostname)) {
        console.log(`捕获到目标域名HTTPS请求: ${hostname}`);
        
        // 对于HTTPS，我们需要建立隧道连接
        const serverSocket = net.connect(port || 443, hostname, () => {
            clientSocket.write('HTTP/1.1 200 Connection Established\r\n\r\n');
            
            // 监听数据传输
            let requestData = '';
            let responseData = '';
            
            clientSocket.on('data', (data) => {
                requestData += data.toString();
                serverSocket.write(data);
            });
            
            serverSocket.on('data', (data) => {
                responseData += data.toString();
                clientSocket.write(data);
            });
            
            // 连接结束时保存数据
            const cleanup = () => {
                if (requestData || responseData) {
                    const timestamp = Date.now();
                    const content = `=== HTTPS隧道数据 ===\n` +
                                   `=== 抓包时间: ${new Date(timestamp).toLocaleString()} ===\n` +
                                   `=== 域名: ${hostname} ===\n\n` +
                                   `=== 请求数据 (加密) ===\n${requestData.substring(0, 1000)}...\n\n` +
                                   `=== 响应数据 (加密) ===\n${responseData.substring(0, 1000)}...\n\n` +
                                   `=== 结束 ===\n`;
                    
                    const fileName = generateFileName(hostname, timestamp);
                    const filePath = path.join(SAVE_DIR, fileName);
                    fs.writeFileSync(filePath, content, 'utf8');
                    console.log(`HTTPS数据已保存到: ${filePath}`);
                }
            };
            
            clientSocket.on('end', cleanup);
            serverSocket.on('end', cleanup);
        });
        
        serverSocket.on('error', (err) => {
            console.error('服务器连接错误:', err);
            clientSocket.end();
        });
    } else {
        // 非目标域名，建立正常隧道
        const serverSocket = net.connect(port || 443, hostname, () => {
            clientSocket.write('HTTP/1.1 200 Connection Established\r\n\r\n');
            serverSocket.pipe(clientSocket);
            clientSocket.pipe(serverSocket);
        });
        
        serverSocket.on('error', (err) => {
            console.error('隧道连接错误:', err);
            clientSocket.end();
        });
    }
});

const PORT = 8888;
server.listen(PORT, () => {
    console.log(`抓包代理服务器已启动，监听端口: ${PORT}`);
    console.log(`目标域名: ${TARGET_DOMAINS.join(', ')}`);
    console.log(`数据保存目录: ${SAVE_DIR}`);
    console.log(`\n请将浏览器代理设置为: 127.0.0.1:${PORT}`);
    console.log(`\n按 Ctrl+C 停止服务器`);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});
