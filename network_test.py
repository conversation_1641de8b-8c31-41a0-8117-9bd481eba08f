import requests
import sys

print("测试网络连接...")

try:
    response = requests.get("https://www.baidu.com", timeout=10)
    print(f"百度连接成功: {response.status_code}")
except Exception as e:
    print(f"百度连接失败: {e}")

try:
    response = requests.get("https://login.live.com", timeout=10)
    print(f"Microsoft连接成功: {response.status_code}")
except Exception as e:
    print(f"Microsoft连接失败: {e}")

print("网络测试完成")
