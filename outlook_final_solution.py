#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Outlook邮箱登录的最终解决方案
专注于成功登录，并提供获取邮件的基础框架
"""

import requests
import re
import json
import base64
import hashlib
import secrets
import urllib.parse
from bs4 import BeautifulSoup
import time

class OutlookFinalSolution:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
        })
        
        # 固定参数
        self.client_id = "9199bf20-a13f-4107-85dc-02114787ef48"
        self.redirect_uri = "https://outlook.live.com/mail/"
        self.scope = "https://outlook.office.com/.default openid profile offline_access"
        
        # 动态参数
        self.uaid = None
        self.ppft_token = None
        self.url_post = None
        self.context_params = {}
        self.session_cookies = {}

    def generate_oauth_params(self, username):
        """生成OAuth参数"""
        self.uaid = secrets.token_hex(16)
        nonce = f"{secrets.token_hex(4)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(6)}"
        
        code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        challenge_bytes = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        code_challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')
        
        state_data = {
            "id": f"{secrets.token_hex(4)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(6)}",
            "meta": {"interactionType": "redirect"}
        }
        state = base64.b64encode(json.dumps(state_data).encode()).decode()
        
        return {
            'client_id': self.client_id,
            'scope': self.scope,
            'redirect_uri': self.redirect_uri,
            'response_type': 'code',
            'state': state,
            'response_mode': 'fragment',
            'nonce': nonce,
            'code_challenge': code_challenge,
            'code_challenge_method': 'S256',
            'uaid': self.uaid,
            'username': username,
            'login_hint': username
        }

    def step1_get_login_page(self, username):
        """第1步：获取登录页面和PPFT令牌"""
        print("第1步：获取登录页面...")
        
        params = self.generate_oauth_params(username)
        url = "https://login.live.com/oauth20_authorize.srf"
        
        headers = {'Referer': 'https://login.microsoftonline.com/'}
        response = self.session.get(url, params=params, headers=headers, timeout=30)
        
        if response.status_code != 200:
            raise Exception(f"获取登录页面失败: {response.status_code}")
        
        print(f"✅ 登录页面获取成功")
        self.extract_login_params(response.text)
        
        if not self.ppft_token:
            raise Exception("未能获取PPFT令牌")
        
        print(f"✅ PPFT令牌: {self.ppft_token[:50]}...")
        return True

    def extract_login_params(self, html_content):
        """从HTML中提取登录参数"""
        # 提取PPFT令牌
        sft_match = re.search(r'sFTTag:["\']([^"\']+)["\']', html_content)
        if sft_match:
            sft_content = sft_match.group(1)
            ppft_in_sft = re.search(r'value=["\']([^"\']+)["\']', sft_content)
            if ppft_in_sft:
                self.ppft_token = ppft_in_sft.group(1)
        
        if not self.ppft_token:
            ppft_input_match = re.search(r'<input[^>]*name=["\']PPFT["\'][^>]*value=["\']([^"\']+)["\']', html_content)
            if ppft_input_match:
                self.ppft_token = ppft_input_match.group(1)
        
        # 提取urlPost
        url_post_match = re.search(r'urlPost:["\']([^"\']+)["\']', html_content)
        if url_post_match:
            self.url_post = url_post_match.group(1)
        
        # 提取上下文参数
        if self.url_post:
            parsed_url = urllib.parse.urlparse(self.url_post)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            for key, values in query_params.items():
                if values:
                    self.context_params[key] = values[0]

    def step2_password_login(self, username, password):
        """第2步：提交用户名密码"""
        print("第2步：提交用户名密码...")
        
        if not self.url_post:
            raise Exception("没有POST URL")
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://login.live.com',
            'Referer': 'https://login.live.com/',
        }
        
        data = {
            'ps': '2',
            'PPFT': self.ppft_token,
            'PPSX': 'Passport',
            'NewUser': '1',
            'login': username,
            'loginfmt': username,
            'passwd': password,
            'type': '11',
            'LoginOptions': '3',
            'IsFidoSupported': '1',
            'isSignupPost': '0',
            'isRecoveryAttemptPost': '0',
        }
        
        response = self.session.post(self.url_post, headers=headers, data=data, timeout=30)
        
        if response.status_code != 200:
            raise Exception(f"密码验证失败: {response.status_code}")
        
        print(f"✅ 密码验证响应: {response.status_code}")
        
        # 处理可能的重定向
        if 'fmHF' in response.text or 'DoSubmit' in response.text:
            print("✅ 处理保持登录状态...")
            return self.handle_keep_signed_in(response)
        
        return True

    def handle_keep_signed_in(self, response):
        """处理保持登录状态"""
        soup = BeautifulSoup(response.text, 'html.parser')
        form = soup.find('form', {'name': 'fmHF'})
        
        if not form:
            return True
        
        action_url = form.get('action')
        form_data = {}
        
        for input_elem in form.find_all('input', {'type': 'hidden'}):
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            if name:
                form_data[name] = value
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://login.live.com',
            'Referer': response.url,
        }
        
        redirect_response = self.session.post(action_url, headers=headers, data=form_data, timeout=30)
        
        if redirect_response.status_code in [200, 302, 303]:
            print("✅ 重定向处理成功")
            return True
        
        raise Exception(f"重定向处理失败: {redirect_response.status_code}")

    def step3_access_outlook(self):
        """第3步：访问Outlook邮箱"""
        print("第3步：访问Outlook邮箱...")
        
        url = "https://outlook.live.com/mail/"
        headers = {
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Dest': 'document',
        }
        
        response = self.session.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print("✅ 成功访问Outlook邮箱")
            
            if 'loadingScreen' in response.text and 'Outlook' in response.text:
                print("✅ 确认进入Outlook主页")
                
                # 保存重要的Cookie信息
                self.save_session_info()
                return True
            else:
                print("⚠️ 可能还在登录流程中")
                return False
        
        raise Exception(f"访问Outlook失败: {response.status_code}")

    def save_session_info(self):
        """保存会话信息"""
        print("💾 保存会话信息...")
        
        # 保存所有Cookie
        for cookie in self.session.cookies:
            self.session_cookies[cookie.name] = cookie.value
            if cookie.name in ['ClientId', 'MSPRequ', 'MSPOK']:
                print(f"✅ 重要Cookie - {cookie.name}: {cookie.value[:30]}...")
        
        print(f"✅ 总共保存了 {len(self.session_cookies)} 个Cookie")

    def complete_login_flow(self, username, password):
        """完整的登录流程"""
        try:
            print(f"开始Outlook登录流程")
            print(f"用户: {username}")
            print("=" * 50)
            
            # 执行登录步骤
            self.step1_get_login_page(username)
            time.sleep(1)
            
            self.step2_password_login(username, password)
            time.sleep(1)
            
            success = self.step3_access_outlook()
            
            if success:
                print("=" * 50)
                print("🎉 登录成功！")
                print("✅ 已成功进入Outlook邮箱")
                print("✅ 会话信息已保存")
                print("\n📋 下一步可以做的事情:")
                print("1. 使用浏览器开发者工具分析邮件API")
                print("2. 研究Outlook的JavaScript代码")
                print("3. 使用Selenium等工具进行进一步操作")
                print("4. 分析网络请求找到正确的邮件获取方法")
                return True
            else:
                print("⚠️ 登录可能未完全成功")
                return False
                
        except Exception as e:
            print(f"❌ 登录失败: {str(e)}")
            raise

def main():
    """主函数"""
    print("Outlook邮箱登录最终解决方案")
    print("=" * 50)
    print("此工具专注于成功登录Outlook邮箱")
    print("登录成功后，可以基于此会话进行进一步开发")
    print()
    
    # 设置登录信息
    username = input("请输入邮箱地址: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return
    
    client = OutlookFinalSolution()
    
    try:
        success = client.complete_login_flow(username, password)
        
        if success:
            print("\n" + "=" * 50)
            print("🎉 恭喜！登录流程完全成功！")
            print("\n📊 会话统计:")
            print(f"- Cookie数量: {len(client.session_cookies)}")
            print(f"- 会话ID: {client.uaid}")
            print(f"- 上下文参数: {len(client.context_params)}")
            
            print("\n💡 提示:")
            print("现在你已经有了一个完全认证的会话")
            print("可以基于这个会话继续开发邮件获取功能")
            print("建议使用浏览器开发者工具分析真实的邮件请求")
        else:
            print("\n⚠️ 登录流程需要进一步调试")
            
    except Exception as e:
        print(f"\n❌ 登录失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
