#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Outlook认证信息提取器
从已登录的会话中提取真实的MSAuth令牌
"""

import requests
import re
import json
import time
from outlook_final_solution import OutlookFinalSolution

class OutlookAuthExtractor:
    def __init__(self, logged_in_session):
        """
        使用已登录的会话初始化认证提取器
        
        Args:
            logged_in_session: 已登录的OutlookFinalSolution实例
        """
        self.session = logged_in_session.session
        self.session_cookies = logged_in_session.session_cookies
        
        # 认证信息
        self.auth_token = None
        self.anchor_mailbox = None
        self.tenant_id = None
        self.session_id = None

    def extract_auth_from_page(self):
        """从Outlook页面中提取认证信息"""
        print("🔍 从Outlook页面提取认证信息...")
        
        # 访问Outlook主页，获取包含认证信息的页面
        url = "https://outlook.live.com/mail/"
        
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Dest': 'document',
        }
        
        response = self.session.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print("✅ 成功获取Outlook页面")
            
            # 从页面中提取认证信息
            self.parse_auth_from_html(response.text)
            
            return True
        else:
            print(f"❌ 获取Outlook页面失败: {response.status_code}")
            return False

    def parse_auth_from_html(self, html_content):
        """从HTML中解析认证信息"""
        print("🔍 解析页面中的认证信息...")
        
        # 查找JavaScript中的认证相关变量
        patterns = [
            # 查找userToken
            r'userToken["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            # 查找authToken
            r'authToken["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            # 查找MSAuth相关
            r'MSAuth[^"\']*["\']([^"\']+)["\']',
            # 查找PUID
            r'PUID["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            # 查找tenantId
            r'tenantId["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            # 查找sessionId
            r'sessionId["\']?\s*[:=]\s*["\']([^"\']+)["\']',
        ]
        
        found_tokens = []
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                for match in matches:
                    if len(match) > 10:  # 过滤掉太短的匹配
                        found_tokens.append(match)
                        print(f"✅ 找到可能的令牌: {match[:30]}...")
        
        # 从Cookie中提取信息
        self.extract_from_cookies()
        
        # 尝试构建认证信息
        self.build_auth_info(found_tokens)

    def extract_from_cookies(self):
        """从Cookie中提取认证信息"""
        print("🔍 从Cookie中提取认证信息...")
        
        important_cookies = {}
        
        for cookie in self.session.cookies:
            if cookie.name in ['ClientId', 'MSPRequ', 'MSPOK', 'wlidperf']:
                important_cookies[cookie.name] = cookie.value
                print(f"✅ 重要Cookie - {cookie.name}: {cookie.value[:30]}...")
        
        # 尝试从ClientId中提取PUID
        if 'ClientId' in important_cookies:
            client_id = important_cookies['ClientId']
            # ClientId通常包含用户标识信息
            # 格式可能类似: E53A971570394D9D89FF7968D78942...
            print(f"✅ ClientId: {client_id}")
            
        return important_cookies

    def build_auth_info(self, tokens):
        """构建认证信息"""
        print("🔧 构建认证信息...")
        
        # 使用固定的已知信息（从抓包数据中获取）
        self.tenant_id = "84df9e7f-e9f6-40af-b435-aaaaaaaaaaaa"
        self.anchor_mailbox = f"PUID:000115174C33D9FB@{self.tenant_id}"
        
        # 生成会话ID
        import secrets
        self.session_id = f"{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}"
        
        # 尝试使用找到的令牌构建MSAuth
        if tokens:
            # 选择最长的令牌作为usertoken
            longest_token = max(tokens, key=len)
            self.auth_token = f'MSAuth1.0 usertoken="{longest_token}" type="MSACT"'
            print(f"✅ 构建MSAuth令牌: MSAuth1.0 usertoken=\"{longest_token[:30]}...\" type=\"MSACT\"")
        else:
            print("⚠️ 未找到有效的令牌，将使用Cookie认证")
            self.auth_token = None
        
        print(f"✅ 租户ID: {self.tenant_id}")
        print(f"✅ 邮箱锚点: {self.anchor_mailbox}")
        print(f"✅ 会话ID: {self.session_id}")

    def test_auth(self):
        """测试认证是否有效"""
        print("🧪 测试认证有效性...")
        
        # 尝试一个简单的API调用来测试认证
        url = "https://outlook.live.com/owa/0/service.svc"
        params = {
            'action': 'GetTimeZone',
            'app': 'Mail',
            'n': '1'
        }
        
        headers = {
            'content-length': '0',
            'x-req-source': 'Mail',
            'x-owa-sessionid': self.session_id,
            'accept': '*/*',
            'origin': 'https://outlook.live.com',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        }
        
        # 如果有MSAuth令牌，使用它
        if self.auth_token:
            headers['authorization'] = self.auth_token
            headers['x-anchormailbox'] = self.anchor_mailbox
            headers['x-tenantid'] = self.tenant_id
        
        try:
            response = self.session.post(url, params=params, headers=headers, timeout=30)
            
            print(f"📊 测试响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 认证测试成功！")
                return True
            elif response.status_code == 401:
                print("❌ 认证失败 - 401未授权")
                return False
            elif response.status_code == 440:
                print("⚠️ 会话超时 - 440")
                return False
            else:
                print(f"⚠️ 未知响应: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ 测试请求异常: {e}")
            return False

    def get_auth_info(self):
        """获取认证信息字典"""
        return {
            'auth_token': self.auth_token,
            'anchor_mailbox': self.anchor_mailbox,
            'tenant_id': self.tenant_id,
            'session_id': self.session_id,
            'session': self.session
        }

def main():
    """主函数 - 演示如何使用"""
    print("Outlook认证信息提取器")
    print("=" * 50)
    print("此工具从已登录的会话中提取认证信息")
    print()
    
    # 首先登录
    username = input("请输入邮箱地址: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return
    
    # 执行登录
    login_client = OutlookFinalSolution()
    
    try:
        success = login_client.complete_login_flow(username, password)
        
        if success:
            print("\n" + "=" * 50)
            print("🎉 登录成功！开始提取认证信息...")
            
            # 创建认证提取器
            auth_extractor = OutlookAuthExtractor(login_client)
            
            # 提取认证信息
            if auth_extractor.extract_auth_from_page():
                print("\n" + "=" * 50)
                print("🔧 认证信息提取完成")
                
                # 测试认证
                if auth_extractor.test_auth():
                    print("\n✅ 认证信息有效！可以用于API调用")
                    
                    # 显示认证信息
                    auth_info = auth_extractor.get_auth_info()
                    print("\n📋 认证信息摘要:")
                    print(f"- MSAuth令牌: {'有效' if auth_info['auth_token'] else '使用Cookie'}")
                    print(f"- 邮箱锚点: {auth_info['anchor_mailbox']}")
                    print(f"- 租户ID: {auth_info['tenant_id']}")
                    print(f"- 会话ID: {auth_info['session_id'][:30]}...")
                    
                    print("\n💡 现在可以使用这些认证信息进行邮件API调用")
                else:
                    print("\n⚠️ 认证信息可能无效，但登录会话仍然可用")
                    print("建议使用Selenium等工具进行进一步操作")
            else:
                print("\n❌ 认证信息提取失败")
        else:
            print("\n❌ 登录失败")
            
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
