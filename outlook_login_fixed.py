#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版本的Outlook邮箱登录流程
基于真实抓包数据分析，确保获取到真正的登录页面
"""

import requests
import re
import json
import base64
import hashlib
import secrets
import urllib.parse
from bs4 import BeautifulSoup
import time

class OutlookLoginFixed:
    def __init__(self):
        # 创建全新的会话，确保没有任何缓存
        self.session = requests.Session()
        
        # 设置与原始请求完全一致的头部
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
        })
        
        # 固定参数（从抓包数据中提取）
        self.client_id = "9199bf20-a13f-4107-85dc-02114787ef48"
        self.redirect_uri = "https://outlook.live.com/mail/"
        self.scope = "https://outlook.office.com/.default openid profile offline_access"
        
        # 动态参数
        self.uaid = None
        self.state = None
        self.nonce = None
        self.code_verifier = None
        self.code_challenge = None
        self.ppft_token = None
        self.context_params = {}
        
    def generate_session_params(self):
        """生成会话参数，与原始抓包数据格式一致"""
        # 生成UAID（32位十六进制）
        self.uaid = secrets.token_hex(16)
        
        # 生成nonce（UUID格式）
        self.nonce = f"{secrets.token_hex(8)[:8]}-{secrets.token_hex(4)[:4]}-{secrets.token_hex(4)[:4]}-{secrets.token_hex(4)[:4]}-{secrets.token_hex(12)[:12]}"
        
        # 生成PKCE参数
        self.code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        challenge_bytes = hashlib.sha256(self.code_verifier.encode('utf-8')).digest()
        self.code_challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')
        
        # 生成state参数（与原始格式一致）
        state_data = {
            "id": f"{secrets.token_hex(8)[:8]}-{secrets.token_hex(4)[:4]}-{secrets.token_hex(4)[:4]}-{secrets.token_hex(4)[:4]}-{secrets.token_hex(12)[:12]}",
            "meta": {"interactionType": "redirect"}
        }
        self.state = base64.b64encode(json.dumps(state_data).encode()).decode()

    def test_oauth_request(self, username):
        """测试OAuth请求，确保获取到登录页面"""
        print("🔄 测试OAuth请求...")
        
        # 完全清除所有Cookie和缓存
        self.session.cookies.clear()
        
        # 生成参数
        self.generate_session_params()
        
        # 构建与原始请求完全一致的参数
        params = {
            'client_id': self.client_id,
            'scope': self.scope,
            'redirect_uri': self.redirect_uri,
            'response_type': 'code',
            'state': self.state,
            'response_mode': 'fragment',
            'nonce': self.nonce,
            'code_challenge': self.code_challenge,
            'code_challenge_method': 'S256',
            'x-client-SKU': 'msal.js.browser',
            'x-client-Ver': '4.14.0',
            'uaid': self.uaid,
            'msproxy': '1',
            'issuer': 'mso',
            'tenant': 'common',
            'ui_locales': 'zh-CN',
            'client_info': '1',
            'jshs': '1',
            'fl': 'dob,flname,wld',
            'cobrandid': 'ab0455a0-8d03-46b9-b18b-df2f57b9e44c',
            'claims': '{"access_token":{"xms_cc":{"values":["CP1"]}}}',
            'username': username,
            'login_hint': username
        }
        
        # 设置Referer为microsoftonline.com（模拟从Azure AD重定向）
        headers = {
            'Referer': 'https://login.microsoftonline.com/',
        }
        
        url = "https://login.live.com/oauth20_authorize.srf"
        
        print(f"📋 请求URL: {url}")
        print(f"📋 参数数量: {len(params)}")
        print(f"📋 UAID: {self.uaid}")
        print(f"📋 用户名: {username}")
        
        try:
            response = self.session.get(url, params=params, headers=headers, timeout=30)
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📊 响应大小: {len(response.text)} 字符")
            print(f"📊 Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            
            # 保存响应用于调试
            with open('oauth_response_fixed.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("💾 响应已保存到 oauth_response_fixed.html")
            
            # 分析响应内容
            self.analyze_response(response)
            
            return response
            
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            raise

    def analyze_response(self, response):
        """分析响应内容，查找关键信息"""
        print("\n🔍 分析响应内容...")
        
        text = response.text
        
        # 检查页面类型
        if 'ServerData' in text:
            print("✅ 检测到ServerData - 这是登录页面")
        elif 'loadingScreen' in text and 'Outlook' in text:
            print("⚠️ 检测到Outlook主页 - 用户可能已认证")
        else:
            print("❓ 未知页面类型")
        
        # 查找PPFT令牌
        soup = BeautifulSoup(text, 'html.parser')
        
        # 方法1: 查找input标签
        ppft_input = soup.find('input', {'name': 'PPFT'})
        if ppft_input:
            ppft_value = ppft_input.get('value')
            print(f"✅ 找到PPFT (input): {ppft_value[:50]}...")
            self.ppft_token = ppft_value
        
        # 方法2: 查找sFTTag
        sft_match = re.search(r'sFTTag:["\']([^"\']+)["\']', text)
        if sft_match:
            sft_content = sft_match.group(1)
            print(f"✅ 找到sFTTag: {len(sft_content)} 字符")
            
            # 从sFTTag中提取PPFT
            ppft_in_sft = re.search(r'value=["\']([^"\']+)["\']', sft_content)
            if ppft_in_sft:
                ppft_value = ppft_in_sft.group(1)
                print(f"✅ 从sFTTag提取PPFT: {ppft_value[:50]}...")
                self.ppft_token = ppft_value
        
        # 查找ServerData
        server_data_match = re.search(r'var ServerData = ({.*?});', text, re.DOTALL)
        if server_data_match:
            print("✅ 找到ServerData")
            server_data_str = server_data_match.group(1)
            
            # 提取关键参数
            url_post_match = re.search(r'urlPost:["\']([^"\']+)["\']', server_data_str)
            if url_post_match:
                url_post = url_post_match.group(1)
                print(f"✅ 找到urlPost: {url_post[:80]}...")
                
                # 解析URL参数
                parsed_url = urllib.parse.urlparse(url_post)
                query_params = urllib.parse.parse_qs(parsed_url.query)
                
                for key, values in query_params.items():
                    if values:
                        self.context_params[key] = values[0]
                        print(f"   {key}: {values[0]}")
        
        # 检查Cookie
        cookies = response.cookies
        if cookies:
            print(f"🍪 设置了 {len(cookies)} 个Cookie:")
            for cookie in cookies:
                print(f"   {cookie.name}: {cookie.value[:30]}...")
        
        # 总结
        if self.ppft_token:
            print(f"\n✅ 成功获取PPFT令牌: {len(self.ppft_token)} 字符")
        else:
            print("\n❌ 未能获取PPFT令牌")
        
        if self.context_params:
            print(f"✅ 获取了 {len(self.context_params)} 个上下文参数")
        else:
            print("❌ 未能获取上下文参数")

def main():
    """主函数"""
    print("Outlook登录修复版本测试")
    print("=" * 50)
    
    username = input("请输入邮箱地址: ").strip()
    if not username:
        username = "<EMAIL>"  # 默认测试邮箱
    
    client = OutlookLoginFixed()
    
    try:
        response = client.test_oauth_request(username)
        
        print("\n" + "=" * 50)
        print("测试完成！")
        
        if client.ppft_token:
            print("✅ 成功获取登录页面和PPFT令牌")
            print("可以继续实现密码验证步骤")
        else:
            print("❌ 未能获取登录页面")
            print("可能需要调整请求参数或处理反爬虫机制")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
