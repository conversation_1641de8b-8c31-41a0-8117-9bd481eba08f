const readline = require('readline');
const { spawn } = require('child_process');
const path = require('path');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

console.log('🔧 本地抓包工具启动器');
console.log('='.repeat(50));
console.log('请选择要使用的抓包工具版本:');
console.log('');
console.log('1. 基础版本 (capture.js)');
console.log('   - 简单的HTTP/HTTPS代理');
console.log('   - 适合基本的抓包需求');
console.log('');
console.log('2. 高级版本 (advanced-capture.js) [推荐]');
console.log('   - 更好的错误处理');
console.log('   - 详细的TLS信息解析');
console.log('   - 更友好的输出格式');
console.log('');

rl.question('请输入选择 (1 或 2): ', (answer) => {
    let scriptFile;
    
    switch(answer.trim()) {
        case '1':
            scriptFile = 'capture.js';
            console.log('🚀 启动基础版本抓包工具...');
            break;
        case '2':
        default:
            scriptFile = 'advanced-capture.js';
            console.log('🚀 启动高级版本抓包工具...');
            break;
    }
    
    rl.close();
    
    // 启动选择的脚本
    const child = spawn('node', [scriptFile], {
        stdio: 'inherit',
        cwd: __dirname
    });
    
    child.on('error', (err) => {
        console.error('❌ 启动失败:', err.message);
        process.exit(1);
    });
    
    child.on('exit', (code) => {
        console.log(`\n📋 程序已退出，退出码: ${code}`);
        process.exit(code);
    });
    
    // 转发信号
    process.on('SIGINT', () => {
        child.kill('SIGINT');
    });
    
    process.on('SIGTERM', () => {
        child.kill('SIGTERM');
    });
});
