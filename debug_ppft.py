#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试PPFT令牌获取问题的专用脚本
"""

import requests
import re
from bs4 import BeautifulSoup

def debug_ppft_extraction():
    """调试PPFT令牌提取"""
    print("=== PPFT令牌提取调试 ===")
    
    # 创建会话
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
    })
    
    # 测试用户名
    username = input("请输入邮箱地址 (用于测试): ").strip()
    if not username:
        username = "<EMAIL>"  # 默认测试邮箱
    
    # OAuth参数 - 使用更完整的参数集
    import secrets
    import base64
    import hashlib

    # 生成PKCE参数
    code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    challenge_bytes = hashlib.sha256(code_verifier.encode('utf-8')).digest()
    code_challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')

    # 生成其他参数
    uaid = secrets.token_hex(16)
    nonce = f"{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}"

    params = {
        'client_id': '9199bf20-a13f-4107-85dc-02114787ef48',
        'scope': 'https://outlook.office.com/.default openid profile offline_access',
        'redirect_uri': 'https://outlook.live.com/mail/',
        'response_type': 'code',
        'response_mode': 'fragment',
        'nonce': nonce,
        'code_challenge': code_challenge,
        'code_challenge_method': 'S256',
        'x-client-SKU': 'msal.js.browser',
        'x-client-Ver': '4.14.0',
        'uaid': uaid,
        'msproxy': '1',
        'issuer': 'mso',
        'tenant': 'common',
        'ui_locales': 'zh-CN',
        'client_info': '1',
        'jshs': '1',
        'fl': 'dob,flname,wld',
        'cobrandid': 'ab0455a0-8d03-46b9-b18b-df2f57b9e44c',
        'claims': '{"access_token":{"xms_cc":{"values":["CP1"]}}}',
        'username': username,
        'login_hint': username,
        'prompt': 'login'  # 强制显示登录页面
    }
    
    try:
        print(f"🔄 请求OAuth授权页面...")
        url = "https://login.live.com/oauth20_authorize.srf"

        # 设置更接近原始请求的头部
        headers = {
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Upgrade-Insecure-Requests': '1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Referer': 'https://login.microsoftonline.com/',
        }

        response = session.get(url, params=params, headers=headers, timeout=30)
        
        print(f"✅ 响应状态码: {response.status_code}")
        print(f"✅ 响应大小: {len(response.text)} 字符")
        
        # 保存完整响应
        with open('debug_oauth_response.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("✅ 完整响应已保存到 debug_oauth_response.html")
        
        # 方法1: 使用BeautifulSoup查找
        print("\n🔍 方法1: BeautifulSoup查找PPFT...")
        soup = BeautifulSoup(response.text, 'html.parser')
        ppft_input = soup.find('input', {'name': 'PPFT'})
        if ppft_input:
            ppft_value = ppft_input.get('value')
            print(f"✅ 找到PPFT (BeautifulSoup): {ppft_value[:50]}...")
        else:
            print("❌ BeautifulSoup未找到PPFT")
        
        # 方法2: 正则表达式查找input标签
        print("\n🔍 方法2: 正则表达式查找input...")
        ppft_pattern = r'<input[^>]*name=["\']PPFT["\'][^>]*value=["\']([^"\']+)["\'][^>]*>'
        ppft_match = re.search(ppft_pattern, response.text, re.IGNORECASE)
        if ppft_match:
            ppft_value = ppft_match.group(1)
            print(f"✅ 找到PPFT (正则input): {ppft_value[:50]}...")
        else:
            print("❌ 正则表达式未找到PPFT input")
        
        # 方法3: 查找所有PPFT相关内容
        print("\n🔍 方法3: 查找所有PPFT相关内容...")
        ppft_all = re.findall(r'PPFT[^"\']*["\']([^"\']+)["\']', response.text, re.IGNORECASE)
        if ppft_all:
            print(f"✅ 找到 {len(ppft_all)} 个PPFT相关值:")
            for i, value in enumerate(ppft_all[:3], 1):  # 只显示前3个
                print(f"   {i}. {value[:50]}...")
        else:
            print("❌ 未找到任何PPFT相关内容")
        
        # 方法4: 查找sFTTag
        print("\n🔍 方法4: 查找sFTTag...")
        sft_pattern = r'sFTTag:["\']([^"\']+)["\']'
        sft_match = re.search(sft_pattern, response.text)
        if sft_match:
            sft_content = sft_match.group(1)
            print(f"✅ 找到sFTTag: {sft_content[:100]}...")
            
            # 从sFTTag中提取PPFT
            ppft_in_sft = re.search(r'value=["\']([^"\']+)["\']', sft_content)
            if ppft_in_sft:
                ppft_value = ppft_in_sft.group(1)
                print(f"✅ 从sFTTag提取PPFT: {ppft_value[:50]}...")
        else:
            print("❌ 未找到sFTTag")
        
        # 方法5: 查找ServerData
        print("\n🔍 方法5: 查找ServerData...")
        server_data_pattern = r'var ServerData = ({.*?});'
        server_data_match = re.search(server_data_pattern, response.text, re.DOTALL)
        if server_data_match:
            server_data = server_data_match.group(1)
            print(f"✅ 找到ServerData: {len(server_data)} 字符")
            
            # 查找ServerData中的关键参数
            context_match = re.search(r'contextid:["\']([^"\']+)["\']', server_data)
            opid_match = re.search(r'opid:["\']([^"\']+)["\']', server_data)
            
            if context_match:
                print(f"✅ contextid: {context_match.group(1)}")
            if opid_match:
                print(f"✅ opid: {opid_match.group(1)}")
        else:
            print("❌ 未找到ServerData")
        
        # 检查页面是否包含登录表单
        print("\n🔍 检查页面内容...")
        if 'login' in response.text.lower():
            print("✅ 页面包含登录相关内容")
        if 'password' in response.text.lower():
            print("✅ 页面包含密码相关内容")
        if 'microsoft' in response.text.lower():
            print("✅ 页面包含Microsoft相关内容")
        
        # 查找所有表单
        forms = soup.find_all('form')
        print(f"✅ 找到 {len(forms)} 个表单")
        for i, form in enumerate(forms, 1):
            action = form.get('action', '无action')
            method = form.get('method', '无method')
            print(f"   表单{i}: {method} -> {action}")
        
        # 查找所有隐藏输入
        hidden_inputs = soup.find_all('input', {'type': 'hidden'})
        print(f"✅ 找到 {len(hidden_inputs)} 个隐藏输入字段")
        for hidden in hidden_inputs[:5]:  # 只显示前5个
            name = hidden.get('name', '无名称')
            value = hidden.get('value', '')
            print(f"   {name}: {value[:30]}...")
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("PPFT令牌提取调试工具")
    print("=" * 40)
    print("此工具将帮助调试PPFT令牌获取问题")
    print("会保存响应内容到文件用于分析")
    print()
    
    debug_ppft_extraction()

if __name__ == "__main__":
    main()
