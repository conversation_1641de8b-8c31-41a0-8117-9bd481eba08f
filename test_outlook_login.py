#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Outlook登录测试脚本
用于测试登录流程的各个步骤
"""

import sys
import json
import requests
from outlook_login_flow import OutlookClient

def test_network_connection():
    """测试网络连接"""
    print("=== 网络连接测试 ===")

    test_urls = [
        "https://login.live.com",
        "https://outlook.live.com",
        "https://login.microsoftonline.com"
    ]

    for url in test_urls:
        try:
            print(f"测试连接: {url}")
            response = requests.get(url, timeout=10)
            print(f"✅ 连接成功 - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False

    print("✅ 所有网络连接测试通过")
    return True

def test_step_by_step():
    """分步测试登录流程"""
    print("=== Outlook登录流程分步测试 ===")
    
    # 这里需要替换为真实的账号信息
    username = input("请输入邮箱地址: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return
    
    client = OutlookClient()
    
    try:
        print("\n🔄 开始测试...")
        
        # 第1步测试
        print("\n📋 第1步: OAuth授权初始化")
        result = client.step1_oauth_authorize(username)
        if result:
            if client.ppft_token:
                print(f"✅ PPFT令牌: {client.ppft_token[:30]}...")
            else:
                print("⚠️ PPFT令牌获取失败")
            print(f"✅ UAID: {client.uaid}")
            print(f"✅ 上下文参数: {client.context_params}")
        else:
            print("❌ OAuth授权初始化失败")
            return
        
        # 第2步测试
        print("\n📋 第2步: 获取实验配置")
        result = client.step2_get_experiments()
        if result:
            print("✅ 实验配置获取成功")
        
        # 第3步测试
        print("\n📋 第3步: 密码验证")
        result = client.step3_password_auth(username, password)
        if result:
            print(f"✅ 密码验证成功，新PPFT: {client.ppft_token[:30]}...")
        
        # 第4步测试
        print("\n📋 第4步: 保持登录确认")
        form_data = client.step4_keep_signed_in()
        if form_data:
            print("✅ 保持登录确认成功")
        
        # 第5步测试
        print("\n📋 第5步: 状态保存")
        result = client.step5_save_state(form_data)
        if result:
            print("✅ 状态保存成功")
        
        # 第6步测试
        print("\n📋 第6步: 访问Outlook")
        result = client.step6_access_outlook()
        if result:
            print(f"✅ Outlook访问成功，会话ID: {client.session_id}")
        
        # 第7步测试
        print("\n📋 第7步: 获取启动数据")
        result = client.step7_get_startup_data()
        if result:
            print(f"✅ 启动数据获取成功")
            print(f"✅ MSAuth令牌: {client.auth_token[:50]}...")
            print(f"✅ 邮箱锚点: {client.anchor_mailbox}")
        
        # 第8步测试
        print("\n📋 第8步: 获取文件夹摘要")
        folder_id = client.step8_get_folder_digest()
        if folder_id:
            print(f"✅ 文件夹摘要获取成功，收件箱ID: {folder_id[:30]}...")
        
        # 第9步测试
        print("\n📋 第9步: 获取文件夹信息")
        result = client.step9_get_folder_info(folder_id)
        if result:
            print("✅ 文件夹信息获取成功")
        
        # 第10步测试
        print("\n📋 第10步: 获取邮件列表")
        emails = client.step10_find_items(folder_id, max_entries=5)
        if emails:
            print("✅ 邮件列表获取成功")
            
            # 尝试解析邮件数据
            try:
                if isinstance(emails, dict):
                    items = emails.get('Body', {}).get('ResponseMessages', {}).get('Items', [])
                    if items:
                        mail_items = items[0].get('RootFolder', {}).get('Items', [])
                        print(f"\n📧 找到 {len(mail_items)} 封邮件:")
                        for i, mail in enumerate(mail_items[:3], 1):  # 只显示前3封
                            subject = mail.get('Subject', '无主题')
                            from_addr = mail.get('From', {}).get('Mailbox', {}).get('EmailAddress', '未知发件人')
                            print(f"  {i}. {subject} (来自: {from_addr})")
                    else:
                        print("📧 收件箱为空")
                else:
                    print("📧 邮件数据格式异常")
            except Exception as e:
                print(f"📧 解析邮件数据时出错: {e}")
        
        print("\n🎉 所有步骤测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_complete_flow():
    """测试完整流程"""
    print("=== Outlook完整登录流程测试 ===")
    
    username = input("请输入邮箱地址: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return
    
    client = OutlookClient()
    
    try:
        print("\n🔄 执行完整登录流程...")
        emails = client.login_and_get_emails(username, password, max_emails=10)
        
        print("\n📧 邮件获取结果:")
        if isinstance(emails, dict):
            # 解析并显示邮件
            try:
                items = emails.get('Body', {}).get('ResponseMessages', {}).get('Items', [])
                if items:
                    mail_items = items[0].get('RootFolder', {}).get('Items', [])
                    print(f"✅ 成功获取 {len(mail_items)} 封邮件")
                    
                    for i, mail in enumerate(mail_items, 1):
                        subject = mail.get('Subject', '无主题')
                        from_info = mail.get('From', {}).get('Mailbox', {})
                        from_addr = from_info.get('EmailAddress', '未知发件人')
                        from_name = from_info.get('Name', from_addr)
                        received_time = mail.get('DateTimeReceived', '未知时间')
                        is_read = mail.get('IsRead', False)
                        has_attachments = mail.get('HasAttachments', False)
                        
                        status = "已读" if is_read else "未读"
                        attachment_info = " 📎" if has_attachments else ""
                        
                        print(f"\n{i}. {subject}{attachment_info}")
                        print(f"   发件人: {from_name} <{from_addr}>")
                        print(f"   时间: {received_time}")
                        print(f"   状态: {status}")
                else:
                    print("📧 收件箱为空")
            except Exception as e:
                print(f"❌ 解析邮件数据失败: {e}")
                print("原始响应数据:")
                print(json.dumps(emails, indent=2, ensure_ascii=False)[:500] + "...")
        else:
            print("❌ 响应格式不正确")
            print(f"响应类型: {type(emails)}")
            print(f"响应内容: {str(emails)[:200]}...")
            
    except Exception as e:
        print(f"\n❌ 完整流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("Outlook登录测试工具")
    print("=" * 40)
    print("1. 网络连接测试")
    print("2. 分步测试 (详细显示每一步)")
    print("3. 完整流程测试")
    print("4. 退出")

    while True:
        choice = input("\n请选择测试模式 (1-4): ").strip()

        if choice == '1':
            test_network_connection()
            break
        elif choice == '2':
            # 先测试网络连接
            if test_network_connection():
                print("\n" + "=" * 40)
                test_step_by_step()
            break
        elif choice == '3':
            # 先测试网络连接
            if test_network_connection():
                print("\n" + "=" * 40)
                test_complete_flow()
            break
        elif choice == '4':
            print("退出程序")
            sys.exit(0)
        else:
            print("❌ 无效选择，请输入 1、2、3 或 4")

if __name__ == "__main__":
    main()
