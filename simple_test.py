#!/usr/bin/env python3
import requests
import re
import json
import base64
import hashlib
import secrets

def test_outlook_login():
    print("开始测试Outlook登录...")
    
    # 创建会话
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Referer': 'https://login.microsoftonline.com/',
    })
    
    # 生成参数
    uaid = secrets.token_hex(16)
    nonce = f"{secrets.token_hex(4)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(6)}"
    
    code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    challenge_bytes = hashlib.sha256(code_verifier.encode('utf-8')).digest()
    code_challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')
    
    state_data = {"id": f"{secrets.token_hex(4)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(6)}", "meta": {"interactionType": "redirect"}}
    state = base64.b64encode(json.dumps(state_data).encode()).decode()
    
    # 请求参数
    params = {
        'client_id': '9199bf20-a13f-4107-85dc-02114787ef48',
        'scope': 'https://outlook.office.com/.default openid profile offline_access',
        'redirect_uri': 'https://outlook.live.com/mail/',
        'response_type': 'code',
        'state': state,
        'response_mode': 'fragment',
        'nonce': nonce,
        'code_challenge': code_challenge,
        'code_challenge_method': 'S256',
        'uaid': uaid,
        'username': '<EMAIL>',
        'login_hint': '<EMAIL>'
    }
    
    url = "https://login.live.com/oauth20_authorize.srf"
    
    try:
        print(f"请求: {url}")
        response = session.get(url, params=params, timeout=30)
        
        print(f"状态码: {response.status_code}")
        print(f"响应大小: {len(response.text)}")
        
        # 保存响应
        with open('simple_test_response.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        # 查找PPFT令牌
        ppft_value = None

        # 方法1: 从sFTTag中提取
        sft_match = re.search(r'sFTTag:["\']([^"\']+)["\']', response.text)
        if sft_match:
            sft_content = sft_match.group(1)
            ppft_in_sft = re.search(r'value=["\']([^"\']+)["\']', sft_content)
            if ppft_in_sft:
                ppft_value = ppft_in_sft.group(1)
                print(f"✅ 从sFTTag找到PPFT: {ppft_value[:50]}...")

        # 方法2: 直接查找input标签
        if not ppft_value:
            ppft_input_match = re.search(r'<input[^>]*name=["\']PPFT["\'][^>]*value=["\']([^"\']+)["\']', response.text)
            if ppft_input_match:
                ppft_value = ppft_input_match.group(1)
                print(f"✅ 从input标签找到PPFT: {ppft_value[:50]}...")

        if not ppft_value:
            print("❌ 未找到PPFT令牌")

        # 查找urlPost
        url_post = None
        url_post_match = re.search(r'urlPost:["\']([^"\']+)["\']', response.text)
        if url_post_match:
            url_post = url_post_match.group(1)
            print(f"✅ 找到urlPost: {url_post[:80]}...")
        else:
            print("❌ 未找到urlPost")
        
        # 检查页面类型
        if 'ServerData' in response.text:
            print("✅ 这是登录页面")
        elif 'loadingScreen' in response.text:
            print("⚠️ 这是Outlook主页")
        else:
            print("❓ 未知页面")
            
        print("响应已保存到 simple_test_response.html")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    test_outlook_login()
