#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实抓包数据的Outlook邮件提取器
使用已登录的会话来获取邮件
"""

import requests
import json
import urllib.parse
import secrets
from outlook_final_solution import OutlookFinalSolution

class OutlookEmailExtractor:
    def __init__(self, logged_in_session):
        """
        使用已登录的会话初始化邮件提取器
        
        Args:
            logged_in_session: 已登录的OutlookFinalSolution实例
        """
        self.session = logged_in_session.session
        self.session_cookies = logged_in_session.session_cookies
        
        # 从抓包数据中提取的关键信息
        self.tenant_id = "84df9e7f-e9f6-40af-b435-aaaaaaaaaaaa"
        self.inbox_folder_id = "AQMkADAwATExADUxNy00YzMzAC1kOWZiLTAwAi0wMAoALgAAAwGz5V/3q8JBoM1efEC//MYBAGGChT0z+8JJrScjqPrv3+MAAAIBDAAAAA=="
        
        # 生成会话参数
        self.session_id = f"{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}"
        self.correlation_id_base = f"wHrfj1H2JBO/9LtoC20DwY"
        
        # 尝试从Cookie中提取认证信息
        self.extract_auth_info()

    def extract_auth_info(self):
        """从Cookie中提取认证信息"""
        print("🔍 从Cookie中提取认证信息...")
        
        # 查找ClientId Cookie
        client_id = self.session_cookies.get('ClientId', '')
        if client_id:
            print(f"✅ 找到ClientId: {client_id[:30]}...")
            # 尝试从ClientId中提取PUID
            # ClientId通常包含用户的PUID信息
            self.anchor_mailbox = f"PUID:000115174C33D9FB@{self.tenant_id}"
        else:
            print("⚠️ 未找到ClientId，使用默认值")
            self.anchor_mailbox = f"PUID:000115174C33D9FB@{self.tenant_id}"
        
        # 生成MSAuth令牌（简化版本，实际需要从真实认证中获取）
        self.auth_token = f"MSAuth1.0 usertoken=\"{secrets.token_hex(32)}\" type=\"MSACT\""
        
        print(f"✅ 邮箱锚点: {self.anchor_mailbox}")

    def get_emails(self, max_emails=25):
        """获取邮件列表"""
        print(f"📧 开始获取最新 {max_emails} 封邮件...")
        
        url = "https://outlook.live.com/owa/0/service.svc"
        params = {
            'action': 'FindItem',
            'app': 'Mail',
            'n': '4'
        }
        
        # 构建请求数据（基于真实抓包数据）
        request_data = {
            "__type": "FindItemJsonRequest:#Exchange",
            "Header": {
                "__type": "JsonRequestHeaders:#Exchange",
                "RequestServerVersion": "V2018_01_08",
                "TimeZoneContext": {
                    "__type": "TimeZoneContext:#Exchange",
                    "TimeZoneDefinition": {
                        "__type": "TimeZoneDefinitionType:#Exchange",
                        "Id": "SE Asia Standard Time"
                    }
                }
            },
            "Body": {
                "ParentFolderIds": [
                    {
                        "__type": "FolderId:#Exchange",
                        "Id": self.inbox_folder_id
                    }
                ],
                "ItemShape": {
                    "__type": "ItemResponseShape:#Exchange",
                    "BaseShape": "IdOnly",
                    "AdditionalProperties": [
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "Subject"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "DateTimeReceived"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "From"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "IsRead"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "HasAttachments"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "Size"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "Importance"},
                        {"__type": "PropertyUri:#Exchange", "FieldURI": "Sender"}
                    ]
                },
                "ShapeName": "MailListItem",
                "Paging": {
                    "__type": "IndexedPageView:#Exchange",
                    "BasePoint": "Beginning",
                    "Offset": 0,
                    "MaxEntriesReturned": max_emails
                },
                "ViewFilter": "All",
                "SortOrder": [
                    {
                        "__type": "SortResults:#Exchange",
                        "Order": "Descending",
                        "Path": {"__type": "PropertyUri:#Exchange", "FieldURI": "ReceivedOrRenewTime"}
                    },
                    {
                        "__type": "SortResults:#Exchange",
                        "Order": "Descending",
                        "Path": {"__type": "PropertyUri:#Exchange", "FieldURI": "DateTimeReceived"}
                    }
                ],
                "FocusedViewFilter": 0,
                "Traversal": "Shallow"
            }
        }
        
        # URL编码请求数据
        encoded_data = urllib.parse.quote(json.dumps(request_data))
        
        # 构建请求头（完全基于抓包数据）
        headers = {
            'content-length': '0',
            'x-req-source': 'Mail',
            'authorization': self.auth_token,
            'action': 'FindItem',
            'x-anchormailbox': self.anchor_mailbox,
            'x-owa-hosted-ux': 'false',
            'ms-cv': f'{self.correlation_id_base}.4',
            'x-owa-sessionid': self.session_id,
            'prefer': 'IdType="ImmutableId", exchange.behavior="IncludeThirdPartyOnlineMeetingProviders"',
            'x-owa-urlpostdata': encoded_data,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'content-type': 'application/json; charset=utf-8',
            'x-owa-correlationid': f"{secrets.token_hex(8)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(4)}-{secrets.token_hex(12)}",
            'accept': '*/*',
            'origin': 'https://outlook.live.com',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9',
            'priority': 'u=1, i'
        }
        
        # 如果有tenant_id，添加到头部
        if self.tenant_id:
            headers['x-tenantid'] = self.tenant_id
        
        print(f"🔄 发送邮件查询请求...")
        print(f"📋 文件夹ID: {self.inbox_folder_id[:30]}...")
        print(f"📋 会话ID: {self.session_id}")
        
        try:
            response = self.session.post(url, params=params, headers=headers, timeout=30)
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📊 响应大小: {len(response.text)} 字符")
            
            if response.status_code == 200:
                print("✅ 邮件查询请求成功")
                
                try:
                    emails_data = response.json()
                    emails = self.parse_emails(emails_data)
                    return emails
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"响应内容: {response.text[:500]}...")
                    return []
            else:
                print(f"❌ 邮件查询失败: {response.status_code}")
                print(f"响应内容: {response.text[:500]}...")
                
                # 如果是认证问题，提供建议
                if response.status_code == 440:
                    print("💡 建议: 440错误通常表示认证问题")
                    print("   - 检查MSAuth令牌是否正确")
                    print("   - 确认x-anchormailbox参数")
                    print("   - 验证会话是否仍然有效")
                
                return []
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return []

    def parse_emails(self, emails_data):
        """解析邮件数据"""
        print("🔍 解析邮件数据...")
        
        emails = []
        
        try:
            if isinstance(emails_data, dict):
                body = emails_data.get('Body', {})
                response_messages = body.get('ResponseMessages', {})
                items = response_messages.get('Items', [])
                
                if items:
                    root_folder = items[0].get('RootFolder', {})
                    mail_items = root_folder.get('Items', [])
                    
                    print(f"✅ 找到 {len(mail_items)} 封邮件")
                    
                    for mail in mail_items:
                        email_info = {
                            'id': mail.get('ItemId', {}).get('Id', ''),
                            'subject': mail.get('Subject', '无主题'),
                            'from': self.extract_from_info(mail.get('From', {})),
                            'sender': self.extract_from_info(mail.get('Sender', {})),
                            'received_time': mail.get('DateTimeReceived', '未知时间'),
                            'is_read': mail.get('IsRead', False),
                            'has_attachments': mail.get('HasAttachments', False),
                            'size': mail.get('Size', 0),
                            'importance': mail.get('Importance', 'Normal'),
                            'item_class': mail.get('ItemClass', ''),
                            'change_key': mail.get('ItemId', {}).get('ChangeKey', '')
                        }
                        emails.append(email_info)
                    
                    print(f"✅ 成功解析 {len(emails)} 封邮件")
                else:
                    print("📧 收件箱为空或无法访问邮件")
            else:
                print("❌ 邮件数据格式异常")
                
        except Exception as e:
            print(f"❌ 解析邮件时出错: {e}")
        
        return emails

    def extract_from_info(self, from_data):
        """提取发件人信息"""
        if isinstance(from_data, dict):
            mailbox = from_data.get('Mailbox', {})
            name = mailbox.get('Name', '')
            email = mailbox.get('EmailAddress', '')
            
            if name and email and name != email:
                return f"{name} <{email}>"
            elif email:
                return email
            elif name:
                return name
        
        return "未知发件人"

def main():
    """主函数 - 演示如何使用"""
    print("Outlook邮件提取器")
    print("=" * 50)
    print("此工具需要先使用OutlookFinalSolution登录")
    print()
    
    # 首先登录
    username = input("请输入邮箱地址: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return
    
    # 执行登录
    login_client = OutlookFinalSolution()
    
    try:
        success = login_client.complete_login_flow(username, password)
        
        if success:
            print("\n" + "=" * 50)
            print("🎉 登录成功！开始获取邮件...")
            
            # 创建邮件提取器
            email_extractor = OutlookEmailExtractor(login_client)
            
            # 获取邮件
            max_emails = input("请输入要获取的邮件数量 (默认10): ").strip()
            try:
                max_emails = int(max_emails) if max_emails else 10
            except:
                max_emails = 10
            
            emails = email_extractor.get_emails(max_emails)
            
            # 显示结果
            if emails:
                print(f"\n📧 邮件列表 (共 {len(emails)} 封):")
                print("=" * 100)
                
                for i, email in enumerate(emails, 1):
                    status = "已读" if email['is_read'] else "未读"
                    attachment = " 📎" if email['has_attachments'] else ""
                    size_kb = email['size'] // 1024 if email['size'] > 0 else 0
                    
                    print(f"{i:2d}. {email['subject']}{attachment}")
                    print(f"     发件人: {email['from']}")
                    if email['sender'] and email['sender'] != email['from']:
                        print(f"     代发人: {email['sender']}")
                    print(f"     时间: {email['received_time']}")
                    print(f"     状态: {status} | 重要性: {email['importance']} | 大小: {size_kb}KB")
                    print(f"     ID: {email['id'][:50]}...")
                    print("-" * 100)
            else:
                print("\n📧 没有获取到邮件")
                print("💡 可能的原因:")
                print("   - 认证令牌需要从真实登录中获取")
                print("   - 需要实现更完整的MSAuth令牌生成")
                print("   - 文件夹ID可能需要动态获取")
        else:
            print("\n❌ 登录失败，无法获取邮件")
            
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
