const express = require("express");
const axios = require("axios");
const https = require("https");
// const cheerio = require("cheerio");
const { HttpsProxyAgent } = require("https-proxy-agent");
const crypto = require("crypto");
const async = require("async");
const NodeRSA = require("node-rsa");
const fs = require("fs");
const { JSDOM } = require("jsdom");
const qs = require("qs");
const FormData = require("form-data");
const WebScraper = require("./WebScraper");
const cors = require("cors");
const app = express();
const port = 3001;
const Queue = require("better-queue");
const rateLimit = require("express-rate-limit");
const RamblerPop = require("./RamblerPop");

// 配置 CORS
app.use(
  cors({
    origin: "http://127.0.0.1:3000", // 允许前端域名
    methods: ["GET", "POST"],
    credentials: true,
  })
);

function unescapeUnicode(str) {
  // 将 Unicode 转义字符（如 \uXXXX）转换为实际的字符
  return str.replace(/\\u([\d\w]{4})/gi, (match, grp) => {
    return String.fromCharCode(parseInt(grp, 16)); // 将 \uXXXX 转换为对应的字符
  });
}
// 处理cookies
function getCookieObjByCookieStr(cookiesArray) {
  // 确保 cookiesArray 是一个数组
  if (!Array.isArray(cookiesArray)) {
    throw new Error("Expected cookiesArray to be an array");
  }

  const cookiesObject = {};

  cookiesArray.forEach((cookieString) => {
    let cookieString_new = cookieString.toString();
    const cookieArray = cookieString_new.split(";");
    const [nameValuePair, ...attributes] = cookieArray;

    // 提取 cookie 的键值对，确保完整值被保留
    const [key, ...valueParts] = nameValuePair.split("=");
    const value = valueParts.join("=").trim(); // 将剩余部分组合为完整值
    if (!key) return;

    // 为 cookie 初始化一个嵌套对象
    cookiesObject[key.trim()] = { value: value || "" };

    // 处理属性
    attributes.forEach((attribute) => {
      const [attrKey, attrValue] = attribute.split("=").map((c) => c.trim());
      if (
        attrKey.toLowerCase() === "secure" ||
        attrKey.toLowerCase() === "httponly"
      ) {
        cookiesObject[key.trim()][attrKey] = true;
      } else {
        cookiesObject[key.trim()][attrKey] = attrValue || "";
      }
    });
  });

  return cookiesObject;
}

function getResponseCookie(response) {
  let res_cookies = response.headers["set-cookie"];
  let obj = getCookieObjByCookieStr(res_cookies);
  return obj;
}

function extractFormDetails(html) {
  //   const $ = cheerio.load(html); // 加载 HTML
  //   const form = $("form"); // 查找表单

  //   if (!form.length) {
  //     throw new Error("No <form> element found in the provided HTML.");
  //   }

  //   // 提取 action 属性
  //   const action = form.attr("action");
  //   if (!action) {
  //     throw new Error("No action attribute found in the <form>.");
  //   }

  //   // 判断 action 是否为完整 URL（即是否以 http 或 https 开头）
  //   const isFullUrl =
  //     action.startsWith("http://") || action.startsWith("https://");

  //   // 提取所有字段
  //   const formData = {};
  //   form.find("input, textarea, select").each((_, element) => {
  //     const name = $(element).attr("name");
  //     const value = $(element).attr("value") || ""; // 默认值为空
  //     if (name) {
  //       formData[name] = value;
  //     }
  //   });

  //   // 返回结果
  //   return {
  //     actionUrl: isFullUrl ? action : `https://default.base.url${action}`, // 如果是完整 URL 就直接返回，否则构造完整的 URL
  //     formData,
  //   };
  try {
    // 使用 JSDOM 解析 HTML
    const dom = new JSDOM(html);
    const document = dom.window.document;

    // 查找表单
    const form = document.querySelector("form");
    if (!form) {
      throw new Error("No <form> element found in the provided HTML.");
    }

    // 提取 action 属性
    const action = form.getAttribute("action");
    if (!action) {
      throw new Error("No action attribute found in the <form>.");
    }

    // 判断 action 是否为完整 URL
    const isFullUrl =
      action.startsWith("http://") || action.startsWith("https://");

    // 提取所有表单字段
    const formData = {};
    const inputs = form.querySelectorAll("input, textarea, select");
    inputs.forEach((element) => {
      const name = element.getAttribute("name");
      const value = element.getAttribute("value") || "";
      if (name) {
        formData[name] = value;
      }
    });

    return {
      actionUrl: isFullUrl ? action : `https://default.base.url${action}`,
      formData,
    };
  } catch (error) {
    console.error("Form extraction error:", error);
    throw error;
  }
}

/**
 * 根据url解析参数
 * @param url
 * @returns {{}}
 */
function parseUrlParams(url) {
  const params = {};

  // 使用 URL 模块解析
  try {
    const urlObj = new URL(url);
    urlObj.searchParams.forEach((value, key) => {
      params[key] = value;
    });
  } catch (error) {
    console.error("URL 解析失败:", error);
  }

  return params;
}

const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// 获取一个代理
const getProxy = async (proxyUrl) => {
  if (proxyUrl == "" || proxyUrl == null || proxyUrl == undefined) {
    return false;
  } else {
    const response = await axios.get(`${proxyUrl}`);
    const IP = response.data.list[0].sever;

    const Port = response.data.list[0].port;
    return { IP, Port };
  }
};

// 生成所有随机数据
const generateRandomData = async (proxyUrl) => {
  const { IP, Port } = await getProxy(proxyUrl);

  return {
    IP,
    Port,
  };
};

// 修改提取函数来获取链接和文本，返回对象形式
function extractTextFromHtml(html) {
  const dom = new JSDOM(html);
  const document = dom.window.document;

  // 获取所有a标签
  const links = document.querySelectorAll("a");

  // 收集所有链接和文本到数组中
  const linkArray = Array.from(links)
    .filter((link) => link.textContent.trim() && link.href) // 过滤掉空的链接
    .map((link) => ({
      text: link.textContent.trim(),
      url: link.href,
    }));

  // 返回对象格式
  return {
    links: linkArray,
    count: linkArray.length,
    hasLinks: linkArray.length > 0,
  };
}

// 添加一个辅助函数来提取HTML中的纯文本
function extractTextFromtHtml(html) {
  const dom = new JSDOM(html);
  return dom.window.document.body.textContent.trim().replace(/\s+/g, " ");
}

// 修改 performRegistration 函数中处理邮件内容的部分
async function performRegistration(
  email,
  password,
  proofs,
  propassword,
  retryCount = 0
) {
  const MAX_RETRIES = 3; // 最大重试次数
  let PPFT, opid, contextid, uaid;
  let cookie100 = "";
  let route;
  let wbids, NAPExp, pprid, wbid, NAP, ANON, ANONExp, t;
  let RPSSecAuth, XCANARY, DefaultAnchorMailbox;
  let ipt, actionurl, canary, linkurl, accountCookies, apiCanary, urlRU;

  let webScraper = new WebScraper();
  let axiosClient = webScraper.axiosInstance;

  try {
    const url = `https://login.live.com/login.srf?wa=wsignin1.0&wreply=https://outlook.live.com/owa/?username=${email}&username=${email}`;

    const headers = {
      Host: "login.live.com",
      "User-Agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
      Accept:
        "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
      "Accept-Encoding": "gzip, deflate, br, zstd",
      "Cache-Control": "max-age=0",
      "Upgrade-Insecure-Requests": "1",
      "Sec-Fetch-Site": "cross-site",
      "Sec-Fetch-Mode": "navigate",
      "Sec-Fetch-User": "?1",
      "Sec-Fetch-Dest": "document",
      "sec-ch-ua":
        '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"Windows"',
      Referer: "https://login.microsoftonline.com/",
    };

    const response = await axiosClient.get(url, { headers });
    // 获取响应头中的 set-cookie
    const cookies = response.headers["set-cookie"];
    if (cookies) {
      for (const cookie of cookies) {
        cookie100 += cookie.substring(0, cookie.indexOf(";")) + ";";
      }
    }
    const html = response.data;

    const sFTTagRegex = /value="([^"]+)"/;
    const matchSft = html.match(sFTTagRegex);
    PPFT = matchSft ? matchSft[1] : "";
    // 添加URL提取逻辑
    const urlRegex =
      /urlPostMsa:'(https:\/\/login\.live\.com\/ppsecure\/post\.srf\?[^']+)'/;
    const match = html.match(urlRegex);
    if (match && match[1]) {
      linkurl = match[1];
    }

    const urlLoginregex = /urlLogin:'(https?:\/\/[^\']+)'/;
    const urlLoginMatch = html.match(urlLoginregex);

    if (urlLoginMatch) {
      // console.log('Extracted URL:', match[1]);
    } else {
      console.log("URL not found.");
    }

    const queryParams = new URLSearchParams(urlLoginMatch[1]);

    const contextId = queryParams.get("contextid");
    const opid = queryParams.get("opid");
    const bk = queryParams.get("bk");
    const mkt = queryParams.get("mkt");
    const lc = queryParams.get("lc");
    uaid = queryParams.get("uaid");

    // Step 2: 登录
    const ppTwoResponse = await ppTwo(email, password);
    cookie100 = getResponseCookie(ppTwoResponse);
    console.log(cookie100, "登录结束");
    let datainfo;
    const htmla = ppTwoResponse.data;

    if (ppTwoResponse.headers["content-type"].includes("text/html")) {
      // 检查密码错误
      if (htmla.includes("你的帐户或密码不正确")) {
        throw new Error("密码不正确!");
      }

      if (htmla.includes("urlPost:")) {
        // 添加URL提取逻辑
        const urlRegex =
          /urlPost:'(https:\/\/login\.live\.com\/ppsecure\/post\.srf\?[^']+)'/;
        const urlmatch = htmla.match(urlRegex);
        if (urlmatch && urlmatch[1]) {
          linkurl = urlmatch[1];
        }
        const sFTregex = /sFT:'([^']+)'/;
        const sFTmatch = htmla.match(sFTregex);

        if (sFTmatch) {
          PPFT = sFTmatch[1]; // 提取到的sFT值
        } else {
          console.log("sFT not found");
          // 尝试从input标签中获取PPFT值
          const inputRegex = /<input\s+type="hidden"\s+name="PPFT"[^>]*value="([^"]+)"/;
          const inputMatch = htmla.match(inputRegex);
          if (inputMatch && inputMatch[1]) {
            PPFT = inputMatch[1]; // 从input标签提取到的PPFT值
            console.log("从input标签获取到PPFT值");
          } else {
            console.log("无法从input标签获取PPFT值");
            throw new Error("无法从input标签获取PPFT值");
          }
        }
        console.log("登陆成功");
      } else {
        // 解析表单数据
        //根据html 获取返回的结果
        datainfo = extractFormDetails(ppTwoResponse.data);
        const actionUrl = datainfo.actionUrl;

        // 检查各种跳转情况
        if (actionUrl.includes("Abuse")) {
          throw new Error("账号已被锁定");
        }

        if (actionUrl.includes("recover")) {
          throw new Error("账号需要恢复");
        }

        if (actionUrl.includes("proofs/Add")) {
          //解析url参数
          let urlParams = parseUrlParams(datainfo.actionUrl)

          //第5次 这个是动态请求
          let options = {
            uaid: uaid,
            pprid: datainfo.formData.pprid,
            ipt: datainfo.formData.ipt,
            opid: opid,
            url: datainfo.actionUrl
          }
          const addFiveResponse = await AddFive(options, axiosClient);
          //判断是否有跳过
          if (!addFiveResponse.data.includes('跳过')) {
            throw new Error("账号需要绑定辅助");
          }

          //继续 跳过请求
          options = {
            uaid: uaid,
            canary: addFiveResponse.canary,
            sn: urlParams.sn,
            ru: urlParams.ru
          }

          try {
            let AddPlused = await AddPlus(options);
            console.log('AddPlus执行成功');
          } catch (error) {
            console.error('AddPlus执行失败，尝试重新执行整个注册流程:', error.message);

            // 检查是否已达到最大重试次数
            if (retryCount < MAX_RETRIES) {
              console.log(`正在进行第 ${retryCount + 1} 次重试...`);
              // 递归调用performRegistration，增加重试计数
              return await performRegistration(email, password, proofs, propassword, retryCount + 1);
            } else {
              console.error(`已达到最大重试次数 ${MAX_RETRIES}，放弃重试`);
              throw new Error(`AddPlus失败，已重试${retryCount}次: ${error.message}`);
            }
          }

        }

        if (actionUrl.includes("identity/confirm")) {
          throw new Error("需要异地验证");
          console.log("需要异地验证");
          // 异地验证第一步
          let options = {
            url: datainfo.actionUrl,
            pprid: datainfo.formData.pprid,
            ipt: datainfo.formData.ipt,
            uaid: uaid,
          };
          const remoteVerificationOneResponse = await remoteVerificationOne(
            options
          );
          if (!remoteVerificationOneResponse) {
            throw new Error("异地验证失败");
          }
          console.log(remoteVerificationOneResponse, "异地验证第一步");
          // 异地验证第二步
          options = {
            epid: remoteVerificationOneResponse.epid,
            canary: remoteVerificationOneResponse.canary,
            tcxt: remoteVerificationOneResponse.tcxt,
            eipt: remoteVerificationOneResponse.eipt,
            uaid: uaid,
            proofs: proofs,
          };
          const remoteVerificationTwoResponse = await remoteVerificationTwo(
            options
          );
          if (!remoteVerificationTwoResponse) {
            throw new Error("异地验证失败");
          }
          console.log(remoteVerificationTwoResponse, "异地验证第二步");

          await delay(5000);

          const getBindEmailMessage = await getBindEmailMessages();
          if (!getBindEmailMessage) {
            throw new Error("获取验证码失败");
          }

          // 异地验证第三步
          options = {
            epid: remoteVerificationOneResponse.epid,
            canary: remoteVerificationTwoResponse.apiCanary,
            tcxt: remoteVerificationTwoResponse.telemetryContext,
            eipt: remoteVerificationOneResponse.eipt,
            uaid: uaid,
            proofs: proofs,
            code: getBindEmailMessage,
          };
          const remoteVerificationThreeResponse = await remoteVerificationThree(
            options
          );
          if (!remoteVerificationThreeResponse) {
            throw new Error("异地验证失败");
          }
          console.log(remoteVerificationThreeResponse, "异地验证第三步");
          // 验证成功后，重新调用 performRegistration
          if (retryCount < MAX_RETRIES) {
            console.log(`异地验证成功，开始第 ${retryCount + 1} 次重新登录...`);
            await delay(3000); // 等待3秒后重试
            return performRegistration(
              email,
              password,
              proofs,
              propassword,
              retryCount + 1
            );
          } else {
            throw new Error("达到最大重试次数，登录失败");
          }
        }

        // 需要同意跨应传输协议
        if (actionUrl.includes("pipl/accrue")) {
          //解析url参数
          let urlParams = parseUrlParams(datainfo.actionUrl)

          //第5次 这个是动态请求
          let options = {
            uaid: uaid,
            pprid: datainfo.formData.pprid,
            ipt: datainfo.formData.ipt,
            opid: opid,
            url: datainfo.actionUrl
          }
          const AddPiplOneResponse = await AddPiplOne(options, axiosClient);
          //  console.log(AddPiplOneResponse, '跨应传输协议')
          //判断是否有跳过
          if (!AddPiplOneResponse) {
            throw new Error("账号需要同意跨应传输协议");
          }

          //继续 跳过请求
          options = {
            uaid: uaid,
            canary: AddPiplOneResponse.canary,
            epid: AddPiplOneResponse.epid,
            tcxt: AddPiplOneResponse.tcxt,
            eipt: AddPiplOneResponse.eipt
          }
          const AddPiplResponse = await AddPipl(options, axiosClient);
          console.log(AddPiplResponse, '跨应传输协议')
          if (!AddPiplResponse.apiCanary) {
            throw new Error("账号需要同意跨应传输协议错误");
          }
          if (retryCount < MAX_RETRIES) {
            console.log(`同意跨应传输协议成功，开始第 ${retryCount + 1} 次重新登录...`);
            await delay(3000); // 等待3秒后重试
            return performRegistration(
              email,
              password,
              proofs,
              propassword,
              retryCount + 1
            );
          } else {
            throw new Error("达到最大重试次数，登录失败");
          }

        }
      }
    } else if (ppTwoResponse.headers["content-type"].includes("text/xml")) {
      throw new Error("发生了未知错误");
    } else {
      throw new Error("未知的响应类型");
    }

    // Step 3:
    const ppThreeResponse = await ppThree();
    if (!ppThreeResponse) {
      throw new Error("访问登陆后连接失败");
    }

    //解析表单
    datainfo = extractFormDetails(ppThreeResponse);
    //不包含跳过 就是7天到了
    //解析url参数

    options = {
      url: datainfo.actionUrl,
      formData: datainfo.formData,
    };
    // Step 4: EvaluateExperimentAssignments
    const ppFourResponse = await ppFour(options);
    if (!ppFourResponse) {
      throw new Error("访问登陆后第二步连接失败");
    }
    if(!ppFourResponse.canaryValue){
      if (retryCount < MAX_RETRIES) {
        console.log(`访问登陆后第二步连接失败，开始第 ${retryCount + 1} 次重新登录...`);
        await delay(3000); // 等待3秒后重试
        return performRegistration(
          email,
          password,
          proofs,
          propassword,
          retryCount + 1
        );
      } else {
        throw new Error("达到最大重试次数，登录失败");
      }
    }

    const ppSixResponse = await ppSix();
    if (!ppSixResponse) {
      throw new Error("获得邮箱类型ID失败");
    }

    // 获取邮箱类型ID
    const inboxType =
      ppSixResponse.data.findFolders.Body.ResponseMessages.Items[0].RootFolder
        .Folders;
    const inboxFolders = inboxType.filter((item) =>
      ["inbox"].includes(item.DistinguishedFolderId)
    );

    const junkFolders = inboxType.filter((item) =>
      ["junkemail"].includes(item.DistinguishedFolderId)
    );

    const inboxFolder = inboxFolders[0].FolderId.Id;
    const junkFolder = junkFolders[0].FolderId.Id;

    //得到码之后就是提交
    options = {
      canary: ppFourResponse.canaryValue,
      inboxFolder: inboxFolder,
    };

    // Step 7: SendOtt
    const ppFiveResponse = await ppFive(options);
    if (!ppFiveResponse) {
      throw new Error("获得邮件失败");
    }
    let allMessages = [];

    const ConversationIds = ppFiveResponse.Body.Conversations.map(
      (item) => item.ConversationId.Id
    );

    if (ConversationIds.length > 0) {
      for (const ConversationId of ConversationIds) {
        try {
          options = {
            canary: ppFourResponse.canaryValue,
            inboxFolder: ConversationId,
          };
          let emailContent = await ppTen(options);
          if (emailContent) {
            allMessages.push(emailContent);
          }
        } catch (error) {
          console.error(
            `处理邮件ID: ${ConversationId} 时出错`,
            error
          );
        }
      }
    }



    // 获取垃圾邮件

    options = {
      canary: ppFourResponse.canaryValue,
      inboxFolder: junkFolder,
    };

    // Step 7: SendOtt
    const junkppFiveResponse = await ppFive(options);
    if (!junkppFiveResponse) {
      throw new Error("获得邮件失败");
    }

    let allJunkMessages = [];

    if (junkppFiveResponse.Body.Conversations.length != 0) {
      const junkConversationIds =
        junkppFiveResponse.Body.Conversations[0].ConversationId.Id;



      try {
        options = {
          canary: ppFourResponse.canaryValue,
          inboxFolder: junkConversationIds,
        };
        let emailContent = await ppTen(options);
        if (emailContent) {
          allJunkMessages.push(emailContent);
        }
      } catch (error) {
        console.error(`处理邮件ID: ${junkConversationIds} 时出错`, error);
      }
    }



    // 修改返回格式
    return {
      success: true,
      email: email,
      inbox: allMessages, // 用分隔符连接所有邮件内容
      junk: allJunkMessages, // 用分隔符连接所有邮件内容
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }

  // Step 2: 登录
  async function ppTwo(email, password, proxy) {
    try {
      let data = {
        method: "POST",
        url: linkurl,
        headers: {
          Accept:
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "Accept-Language": "zh-CN,zh;q=0.9",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
          Origin: "https://login.live.com",
          Pragma: "no-cache",
          Referer: `https://login.live.com/login.srf?wa=wsignin1.0&rpsnv=168&ct=**********&rver=7.5.2156.0&wp=SA_20MIN&wreply=https://account.live.com/password/Change?mkt=zh-CN&refd=account.microsoft.com&refp=security&uaid=${uaid}&lc=1033&id=38936&mkt=zh-CN&uaid=${uaid}`,
          "Sec-Fetch-Dest": "document",
          "Sec-Fetch-Mode": "navigate",
          "Sec-Fetch-Site": "same-origin",
          "Sec-Fetch-User": "?1",
          "Upgrade-Insecure-Requests": "1",
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
          "sec-ch-ua":
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "content-type": "application/x-www-form-urlencoded",
          Host: "login.live.com",
          Cookie: cookie100,
        },
        data: {
          ps: "2",
          psRNGCDefaultType: "",
          psRNGCEntropy: "",
          psRNGCSLK: "",
          canary: "",
          ctx: "",
          hpgrequestid: "",
          PPFT: PPFT,
          PPSX: "Pa",
          NewUser: "1",
          FoundMSAs: "",
          fspost: "0",
          i21: "0",
          CookieDisclosure: "0",
          IsFidoSupported: "1",
          isSignupPost: "0",
          isRecoveryAttemptPost: "0",
          i13: "0",
          login: email,
          loginfmt: email,
          type: "11",
          LoginOptions: "3",
          lrt: "",
          lrtPartition: "",
          hisRegion: "",
          hisScaleUnit: "",
          passwd: password,
        },
      };

      let response = await axiosClient.request(data);
      return response;
      // 获取响应头中的 set-cookie
      // const cookies = response.headers["set-cookie"];
      // cookie100 = "";
      // if (cookies) {
      //   for (const cookie of cookies) {
      //     cookie100 += cookie.substring(0, cookie.indexOf(";")) + ";";
      //     if (cookie.includes("RPSSecAuth=")) {
      //       RPSSecAuth = cookie.split(";")
      //         .find(c => c.trim().startsWith("RPSSecAuth="))
      //         ?.trim() || RPSSecAuth;
      //       console.log("RPSSecAuth:", RPSSecAuth);
      //     }

      //     // 提取 AMRPSSecAuth
      //     if (cookie.includes("AMRPSSecAuth=")) {
      //       AMRPSSecAuth = cookie.split(";")
      //         .find(c => c.trim().startsWith("AMRPSSecAuth="))
      //         ?.trim() || AMRPSSecAuth;
      //       console.log("AMRPSSecAuth:", AMRPSSecAuth);
      //     }
      //   }
      // }

      // const html = response.data;

      // if (html.includes("fmHF") && html.includes("proofs/Add")) {
      //   // 使用 jsdom 解析 HTML
      //   const dom = new JSDOM(html);
      //   const inputs =
      //     dom.window.document.querySelectorAll("input[type=hidden]");
      //   inputs.forEach((input) => {
      //     const value = input.value;
      //     const name = input.name;
      //     const action = input.action;
      //     if (name === "ipt") ipt = value;
      //     if (name === "pprid") pprid = value;
      //     if (name === "uaid") uaid = value;

      //     // 通过 JSDOM 获取 document 对象
      //     const document = dom.window.document;

      //     actionurl = document
      //       .querySelector("form#fmHF")
      //       .getAttribute("action");
      //   });
      //   return true;
      // }
      // if (html.includes("fmHF") && html.includes("identity/confirm")) {
      //   // 使用 jsdom 解析 HTML
      //   const dom = new JSDOM(html);
      //   const inputs =
      //     dom.window.document.querySelectorAll("input[type=hidden]");
      //   inputs.forEach((input) => {
      //     const value = input.value;
      //     const name = input.name;
      //     const action = input.action;
      //     if (name === "ipt") ipt = value;
      //     if (name === "pprid") pprid = value;
      //     if (name === "uaid") uaid = value;

      //     // 通过 JSDOM 获取 document 对象
      //     const document = dom.window.document;

      //     actionurl = document
      //       .querySelector("form#fmHF")
      //       .getAttribute("action");
      //   });

      //    return ppThree(proxy);
      // } else if (html.includes("route=")) {
      //   console.log("登陆成功！");

      //   route = html.substring(
      //     html.indexOf("route=") + 6,
      //     html.indexOf("',sClien")
      //   );
      //   const regex = /sFT:'([^']+)'/;
      //   const match = html.match(regex);

      //   if (match) {
      //     PPFT = match[1]; // 提取到的sFT值
      //   } else {
      //     console.log("sFT not found");
      //   }
      //   return loginOne();
      // }
      // if(html.includes("fmHF") && html.includes("owa/?username")){
      //   // 使用 jsdom 解析 HTML
      // const dom = new JSDOM(html);

      // const inputs = dom.window.document.querySelectorAll("input[type=hidden]");
      // inputs.forEach((input) => {
      //   const value = input.value;
      //   const name = input.name;

      //   if (name === "NAPExp") NAPExp = value;
      //   if (name === "wbids") wbids = value;
      //   if (name === "pprid") pprid = value;
      //   if (name === "wbid") wbid = value;
      //   if (name === "NAP") NAP = value;
      //   if (name === "ANON") ANON = value;
      //   if (name === "ANONExp") ANONExp = value;
      //   if (name === "t") t = value;
      // });

      // // 通过 JSDOM 获取 document 对象
      // const document = dom.window.document;

      // // 使用 document.querySelector 获取 form 标签的 action 属性

      // const element = document.querySelector("form#fmHF");

      // if (element !== null) {
      //   actionurl = document.querySelector("form#fmHF").getAttribute("action");
      // } else {
      //   console.log("未找到 form 标签");
      // }

      // return loginTwo(); // 调用下一个函数
      // }
    } catch (error) {
      console.error("网络操作失败2:", error.message);
      return false;
    }
  }

  // Step 3:
  async function ppThree() {
    try {
      let data = {
        method: "POST",
        url: linkurl,
        headers: {
          accept:
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "accept-language": "zh-CN,zh;q=0.9",
          "cache-control": "max-age=0",
          "content-type": "application/x-www-form-urlencoded",
          origin: "https://login.live.com",
          priority: "u=0, i",
          referer: "https://login.live.com/",
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
          "sec-ch-ua":
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "sec-fetch-dest": "document",
          "sec-fetch-mode": "navigate",
          "sec-fetch-site": "none",
          "sec-fetch-user": "?1",
          "upgrade-insecure-requests": "1",
        },
        data: {
          LoginOptions: "1",
          type: "28",
          ctx: "",
          hpgrequestid: "",
          PPFT: PPFT,
          canary: "",
        },
      };

      let result = await axiosClient.request(data);
      console.log(result.data, "登陆后第一步结果");
      return result.data;
    } catch (error) {
      console.error("发送验证码失败:", error.message);
      return false;
    }
  }

  // Step 4:
  async function ppFour(options) {
    try {
      let data = {
        method: "POST",
        url: options.url,
        headers: {
          Host: "outlook.live.com",
          accept:
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "accept-language": "zh-CN,zh;q=0.9",
          "cache-control": "max-age=0",
          "content-type": "application/x-www-form-urlencoded",
          priority: "u=0, i",
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
          "sec-ch-ua":
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "sec-fetch-dest": "document",
          "sec-fetch-mode": "navigate",
          "sec-fetch-site": "none",
          "sec-fetch-user": "?1",
          "upgrade-insecure-requests": "1",
        },
        data: options.formData,
      };

      console.log("\n=== 发送ppFour请求 ===");
      console.log("请求URL:", options.url);

      let result = await axiosClient.request(data);

      // 获取 canary 值
      const canaryValue = webScraper.getCanaryValue();

      // 将 canary 添加到返回结果中
      return {
        ...result,
        canaryValue,
      };
    } catch (error) {
      console.error("登录第二步失败:", error.response?.status);
      throw error;
    }
  }

  // Step 5:
  async function ppSix() {
    try {
      let data = {
        method: "POST",
        url: "https://outlook.live.com/owa/0/startupdata.ashx?app=Mail&n=0",
        headers: {
          Host: "outlook.live.com",
          accept:
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "accept-language": "zh-CN,zh;q=0.9",
          "cache-control": "max-age=0",
          "content-type": "application/x-www-form-urlencoded",
          origin: "https://outlook.live.com",
          priority: "u=0, i",
          "sec-ch-ua":
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          action: "StartupData",
          "sec-fetch-dest": "document",
          "sec-fetch-mode": "navigate",
          "sec-fetch-site": "same-origin",
          "sec-fetch-user": "?1",
          "upgrade-insecure-requests": "1",
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
        },
      };

      let result = await axiosClient.request(data);

      return result;
      // const info = JSON.stringify(result.data, null, 2); // 格式化 JSON

      // const $ = cheerio.load(result.data);

      // //获取 epid 和eipt
      // const match = result.data.match(/"eipt":"(.*?)"/);

      // let eipt;
      // if (match) {
      //   eipt = match[1];
      //   console.log("提取的 eipt 值:", eipt);
      // } else {
      //   console.log("未找到 eipt 字段");
      // }

      // //获取eipt
      // const actionValue = $("#frmVerifyProof").attr("action");
      // let fiveUrlParams = parseUrlParams(actionValue);
      // let epid = fiveUrlParams.epid;

      // //获取canary

      // let canary;
      // const canarymatch = result.data.match(
      //   /<input[^>]*id="canary"[^>]*value="([^"]*)"/
      // );
      // if (canarymatch) {
      //   console.log("canary 值:", canarymatch[1]);
      //   canary = canarymatch[1];
      // } else {
      //   console.log("未找到 canary 值");
      // }

      // let tcxt;
      // const tcxtmatch = result.data.match(/"tcxt":"([^"]*)"/);
      // if (tcxtmatch) {
      //   console.log("tcxt 值:", tcxtmatch[1]);
      //   tcxt = tcxtmatch[1];
      // } else {
      //   console.log("未找到 tcxt 值");
      // }

      // return {
      //   eipt,
      //   epid,
      //   canary,
      //   tcxt,
      // };
    } catch (error) {
      console.error("网络操作失败5:", error.message);
      return false;
    }
  }

  // 辅助邮箱获取邮件
  async function getBindEmailMessages() {
    try {
      const response = await axios.get(
        `http://mail2.xmdmail.com/api/sj.php?email=${proofs}`,
        {
          httpsAgent: new https.Agent({
            rejectUnauthorized: false,
          }),
          timeout: 20000, // 10秒超时
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
          },
        }
      )

      const content = response.data;

      // 方法1：使用更精确的正则表达式匹配"安全代码："后面的6位数字
      const securityCodeMatch = content.match(/安全代码：.*?(\d{6})/);

      if (securityCodeMatch && securityCodeMatch[1]) {
        return securityCodeMatch[1];
      }



      throw new Error('未找到验证码');
    } catch (error) {
      throw new Error(`HTTP error: ${error.response ? error.response.data : error.message}`)
    }

    // const client = new RamblerPop(proofs, propassword);
    // const maxRetries = 3; // 最大重试次数
    // const retryDelay = 2000; // 重试间隔时间（毫秒）
    // let attempt = 0;

    // while (attempt < maxRetries) {
    //   try {
    //     // 设置更长的超时时间
    //     await client.connect({
    //       timeout: 30000, // 30秒超时
    //     });

    //     console.log("邮箱连接成功，正在获取邮件...");
    //     const emails = await client.getEmails();
    //     await client.disconnect();

    //     // 使用正则表达式匹配"安全代码: "后面的6位数字
    //     const securityCodeMatch = emails.match(/安全代码: \s*(\d{6})/);

    //     if (securityCodeMatch && securityCodeMatch[1]) {
    //       console.log("成功获取验证码");
    //       return securityCodeMatch[1];
    //     } else {
    //       throw new Error("未找到验证码");
    //     }
    //   } catch (error) {
    //     attempt++;
    //     console.error(`第 ${attempt} 次尝试失败:`, error.message);

    //     if (attempt === maxRetries) {
    //       throw new Error(
    //         `获取验证码失败，已重试 ${maxRetries} 次: ${error.message}`
    //       );
    //     }

    //     // 确保断开连接
    //     try {
    //       await client.disconnect();
    //     } catch (disconnectError) {
    //       console.error("断开连接时出错:", disconnectError.message);
    //     }

    //     // 等待一段时间后重试
    //     await new Promise((resolve) => setTimeout(resolve, retryDelay));
    //     console.log(`准备第 ${attempt + 1} 次重试...`);
    //   }
    // }
  }

  // Step 4:
  async function ppFive(options) {
    // const find_item_params = {
    //   __type: "FindConversationJsonRequest:#Exchange",
    //   Header: {
    //     __type: "JsonRequestHeaders:#Exchange",
    //     RequestServerVersion: "V2018_01_08",
    //     TimeZoneContext: {
    //       __type: "TimeZoneContext:#Exchange",
    //       TimeZoneDefinition: {
    //         __type: "TimeZoneDefinitionType:#Exchange",
    //         Id: "Central Standard Time",
    //       },
    //     },
    //   },
    //   Body: {
    //     ParentFolderId: {
    //       __type: "TargetFolderId:#Exchange",
    //       BaseFolderId: {
    //         __type: "FolderId:#Exchange",
    //         Id: options.inboxFolder,
    //       },
    //     },
    //     ConversationShape: {
    //       __type: "ConversationResponseShape:#Exchange",
    //       BaseShape: "IdOnly",
    //     },
    //     ShapeName: "ReactConversationListView",
    //     Paging: {
    //       __type: "IndexedPageView:#Exchange",
    //       BasePoint: "Beginning",
    //       Offset: 0,
    //       MaxEntriesReturned: 25,
    //     },
    //     ViewFilter: "All",
    //     SortOrder: [
    //       {
    //         __type: "SortResults:#Exchange",
    //         Order: "Descending",
    //         Path: {
    //           __type: "PropertyUri:#Exchange",
    //           FieldURI: "ConversationLastDeliveryOrRenewTime",
    //         },
    //       },
    //       {
    //         __type: "SortResults:#Exchange",
    //         Order: "Descending",
    //         Path: {
    //           __type: "PropertyUri:#Exchange",
    //           FieldURI: "ConversationLastDeliveryTime",
    //         },
    //       },
    //     ],
    //     FocusedViewFilter: 0,
    //   },
    // };
    const find_item_params = {
      __type: "FindConversationJsonRequest:#Exchange",
      Header: {
        __type: "JsonRequestHeaders:#Exchange",
        RequestServerVersion: "V2018_01_08",
        TimeZoneContext: {
          __type: "TimeZoneContext:#Exchange",
          TimeZoneDefinition: {
            __type: "TimeZoneDefinitionType:#Exchange",
            Id: "Central Standard Time",
          },
        },
      },
      Body: {
        ParentFolderId: {
          __type: "TargetFolderId:#Exchange",
          BaseFolderId: {
            __type: "FolderId:#Exchange",
            Id: options.inboxFolder,
          },
        },
        ConversationShape: {
          __type: "ConversationResponseShape:#Exchange",
          BaseShape: "IdOnly",
        },
        ShapeName: "ReactConversationListView",
        Paging: {
          __type: "IndexedPageView:#Exchange",
          BasePoint: "Beginning",
          Offset: 0,
          MaxEntriesReturned: 10,
        },
        ViewFilter: "All",
        SortOrder: [
          {
            __type: "SortResults:#Exchange",
            Order: "Descending",
            Path: {
              __type: "PropertyUri:#Exchange",
              FieldURI: "ConversationLastDeliveryOrRenewTime",
            },
          },
          {
            __type: "SortResults:#Exchange",
            Order: "Descending",
            Path: {
              __type: "PropertyUri:#Exchange",
              FieldURI: "ConversationLastDeliveryTime",
            },
          },
        ],
        FocusedViewFilter: 0,
      },
    };
    try {
      let data = {
        method: "POST",
        url: "https://outlook.live.com/owa/0/service.svc?action=FindConversation&app=Mail",
        headers: {
          Host: "outlook.live.com",
          accept:
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "accept-language": "zh-CN,zh;q=0.9",
          priority: "u=0, i",
          "sec-ch-ua":
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "x-owa-canary": options.canary,
          "x-owa-urlpostdata": JSON.stringify(find_item_params),
          "x-req-source": "Mail",
          action: "FindConversation",
          "sec-fetch-dest": "document",
          "sec-fetch-mode": "navigate",
          "sec-fetch-site": "same-origin",
          "sec-fetch-user": "?1",
          "upgrade-insecure-requests": "1",
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
        },
      };

      //获取绑定邮箱
      let result = await axiosClient.request(data);
      return result.data;
    } catch (error) {
      console.error("网络操作失败6:", error.message);
      return false;
    }
  }

  // 修改 ppTen 方法
  async function ppTen(options) {
    const find_item_params = {
      __type: "GetConversationItemsJsonRequest:#Exchange",
      Header: {
        __type: "JsonRequestHeaders:#Exchange",
        RequestServerVersion: "V2017_08_18",
        TimeZoneContext: {
          __type: "TimeZoneContext:#Exchange",
          TimeZoneDefinition: {
            __type: "TimeZoneDefinitionType:#Exchange",
            Id: "Greenwich Standard Time",
          },
        },
      },
      Body: {
        __type: "GetConversationItemsRequest:#Exchange",
        Conversations: [
          {
            __type: "ConversationRequestType:#Exchange",
            ConversationId: {
              __type: "ItemId:#Exchange",
              Id: options.inboxFolder,
            },
            SyncState: "",
          },
        ],

        ShapeName: "ItemPart",
        SortOrder: "DateOrderAscending",
        MaxItemsToReturn: 20,
        Action: "ReturnRootNode",
        FoldersToIgnore: [],
        ReturnSubmittedItems: true,
        ReturnDeletedItems: true,
      },
    };

    try {
      let data = {
        method: "POST",
        url: "https://outlook.live.com/owa/0/service.svc?action=GetConversationItems&app=Mail",
        headers: {
          Host: "outlook.live.com",
          accept:
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "accept-language": "zh-CN,zh;q=0.9",
          priority: "u=0, i",
          "sec-ch-ua":
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "x-owa-canary": options.canary,
          "x-req-source": "Mail",
          action: "GetConversationItems",
          "sec-fetch-dest": "document",
          "sec-fetch-mode": "navigate",
          "sec-fetch-site": "same-origin",
          "sec-fetch-user": "?1",
          "upgrade-insecure-requests": "1",
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
        },
        data: JSON.stringify(find_item_params),
      };

      let result = await axiosClient.request(data);
      const emailContent =
        result.data.Body.ResponseMessages.Items[0].Conversation.ConversationNodes[0]
          .Items[0].Sender.Mailbox.Name;
      if (emailContent.includes("Battle.net")) {
        return result.data.Body.ResponseMessages.Items[0].Conversation.ConversationNodes[0]
        .Items[0].Preview;
      } 
      // // 提取纯文本内容
      // const plainText = extractTextFromHtml(emailContent);
      // const htmlText = extractTextFromtHtml(emailContent);
      // return emailContent;
    } catch (error) {
      console.error("获取邮件内容失败:", error.message);
      return false;
    }
  }

  // 绑定邮箱
  async function AddFive(options, axiosClient) {

    let data = {
      method: 'POST',
      url: options.url,
      headers: {
        accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/x-www-form-urlencoded',
        origin: 'https://login.live.com',
        pragma: 'no-cache',
        priority: 'u=0, i',
        referer: 'https://login.live.com/',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-site',
        'upgrade-insecure-requests': '1',
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
      },
      data: {
        pprid: options.pprid,
        ipt: options.ipt,
        uaid: options.uaid
      }
    };

    let result = await axiosClient.request(data)
    // console.log(result.data, '绑定邮箱第5步')

    let canary
    const regex = /<input[^>]*name=["']canary["'][^>]*value=["']([^"']*)["']/i

    // 提取匹配值
    const match = result.data.match(regex)
    if (match) {
      // console.log('Extracted Value:', match[1]);
      canary = match[1]
    } else {
      console.log('No match found')
    }

    return {
      canary,
      data: result.data
    }
  }


  async function AddPlus(options) {
    let data = {
      method: 'POST',
      url: 'https://account.live.com/proofs/Add',
      params: {
        mkt: 'zh-cn',
        sn: options.sn,
        uiflavor: 'host',
        mpcxt: 'CATB',
        ru: options.ru,
        id: '293577',
        client_id: "1E00004152160E"
      },
      headers: {
        accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'max-age=0',
        'content-type': 'application/x-www-form-urlencoded',
        origin: 'https://account.live.com',
        priority: 'u=0, i',
        referer: `https://account.live.com/proofs/Add?mkt=ZH-CN&uiflavor=host&client_id=1E00004152160E&id=293577&mpcxt=CATB&sn=604618&ru=https://login.live.com/oauth20_authorize.srf?uaid=${options.uaid}&client_id=9e5f94bc-e8a4-4e73-b8be-63364c29d753&opid=C26DD8A6D6933CCB&mkt=ZH-CN&opidt=**********`,
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
        'Cookie': options.cookies || '', // 添加Cookie支持
        'Host': 'account.live.com' // 确保Host头与请求域名一致
      },
      data: {
        iProofOptions: 'Email',
        DisplayPhoneCountryISO: 'CN',
        DisplayPhoneNumber: '',
        EmailAddress: '',
        canary: options.canary,
        action: 'Skip',
        PhoneNumber: '',
        PhoneCountryISO: ''
      },
      withCredentials: true // 启用跨域请求时发送凭据
    }

    try {
      let result = await axiosClient.request(data)
      console.log(result.data)
      return result.data
    } catch (error) {
      console.error('AddPlus请求失败:', error.message)
      if (error.response) {
        console.error('状态码:', error.response.status)
        console.error('响应头:', error.response.headers)
      }
      throw error
    }
  }

  //   异地验证第一步

  async function remoteVerificationOne(options) {
    try {
      let data = {
        method: "POST",
        url: options.url,
        headers: {
          accept:
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "accept-language": "zh-CN,zh;q=0.9",
          "cache-control": "max-age=0",
          "content-type": "application/x-www-form-urlencoded",
          origin: "https://account.live.com",
          priority: "u=0, i",
          "sec-ch-ua":
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "sec-fetch-dest": "document",
          "sec-fetch-mode": "navigate",
          "sec-fetch-site": "same-origin",
          "sec-fetch-user": "?1",
          "upgrade-insecure-requests": "1",
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
        },
        data: {
          pprid: options.pprid,
          ipt: options.ipt,
          uaid: options.uaid,
        },
      };

      let html = await axiosClient.request(data);

      // 反转义 responseData
      const unescapedData = unescapeUnicode(html.data);

      // 使用正则表达式匹配所有的 "epid" 字段
      const regex = /"epid":"([^"]+)"/g;

      let match;
      const epids = [];

      while ((match = regex.exec(unescapedData)) !== null) {
        // 将每个匹配的epid值存储在数组中
        epids.push(match[1]);
      }

      // 打印所有找到的epid
      if (epids.length > 0) {
        let epid = epids[0];

        let canary = unescapedData.match(/"apiCanary":"([^"]+)"/)[1];

        let tcxt = unescapedData.match(/"tcxt":"([^"]+)"/)[1];

        let eipt = unescapedData.match(/"eipt":"([^"]+)"/)[1];

        return {
          eipt,
          epid,
          canary,
          tcxt,
        };
      }
    } catch (error) {
      console.error("网络操作失败6:", error.message);
      return false;
    }
  }

  //   异地验证第二步
  async function remoteVerificationTwo(options) {
    try {
      let data = {
        method: "POST",
        url: "https://account.live.com/API/Proofs/SendOtt",
        headers: {
          "accept-language": "zh-CN,zh;q=0.9",
          "cache-control": "max-age=0",
          origin: "https://account.live.com",
          priority: "u=0, i",
          "sec-ch-ua":
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "sec-fetch-dest": "document",
          "sec-fetch-mode": "navigate",
          "sec-fetch-site": "same-origin",
          "sec-fetch-user": "?1",
          "upgrade-insecure-requests": "1",
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
          "Content-Type": "application/json",
          Accept: "application/json",
          uaid: options.uaid,
          eipt: options.eipt,
          canary: options.canary,
          tcxt: options.tcxt,
        },
        data: {
          token: "",
          purpose: "UnfamiliarLocationHard",
          epid: options.epid.replace(/\\/g, ""),
          autoVerification: false,
          autoVerificationFailed: false,
          confirmProof: options.proofs,
          uiflvr: 1001,
          uaid: options.uaid,
          scid: 100166,
          hpgid: 200368,
        },
      };

      let result = await axiosClient.request(data);

      return result.data;
    } catch (error) {
      console.error("网络操作失败6:", error.message);
      return false;
    }
  }

  //   异地验证第三步
  async function remoteVerificationThree(options) {
    try {
      let data = {
        method: "POST",
        url: "https://account.live.com/API/Proofs/VerifyCode",
        headers: {
          "accept-language": "zh-CN,zh;q=0.9",
          "cache-control": "max-age=0",
          origin: "https://account.live.com",
          priority: "u=0, i",
          "sec-ch-ua":
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "sec-fetch-dest": "document",
          "sec-fetch-mode": "navigate",
          "sec-fetch-site": "same-origin",
          "sec-fetch-user": "?1",
          "upgrade-insecure-requests": "1",
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
          "Content-Type": "application/json",
          Accept: "application/json",
          uaid: options.uaid,
          eipt: options.eipt,
          canary: options.canary,
          tcxt: options.tcxt,
        },
        data: {
          code: options.code,
          action: "IptVerify",
          purpose: "UnfamiliarLocationHard",
          epid: options.epid.replace(/\\/g, ""),
          confirmProof: options.proofs,
          uiflvr: 1001,
          uaid: options.uaid,
          scid: 100166,
          hpgid: 200374,
        },
      };

      let result = await axiosClient.request(data);

      return result.data;
    } catch (error) {
      console.error("网络操作失败6:", error.message);
      return false;
    }
  }

  // 跨境传输协议
  async function AddPiplOne(options, axiosClient) {

    let data = {
      method: 'POST',
      url: options.url,
      headers: {
        accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/x-www-form-urlencoded',
        origin: 'https://login.live.com',
        pragma: 'no-cache',
        priority: 'u=0, i',
        referer: 'https://login.live.com/',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-site',
        'upgrade-insecure-requests': '1',
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
      },
      data: {
        pprid: options.pprid,
        ipt: options.ipt,
        uaid: options.uaid
      }
    };

    let html = await axiosClient.request(data);

    // 反转义 responseData
    const unescapedData = unescapeUnicode(html.data);

    // 使用正则表达式匹配所有的 "epid" 字段
    const regex = /"epid":"([^"]+)"/g;

    let match;
    const epids = [];

    // while ((match = regex.exec(unescapedData)) !== null) {
    //   // 将每个匹配的epid值存储在数组中
    //   epids.push(match[1]);
    // }

    // 打印所有找到的epid
    // if (epids.length > 0) {
    let epid = epids[0];

    let canary = unescapedData.match(/"apiCanary":"([^"]+)"/)[1];

    let tcxt = unescapedData.match(/"tcxt":"([^"]+)"/)[1];

    let eipt = unescapedData.match(/"eipt":"([^"]+)"/)[1];

    return {
      eipt,
      epid,
      canary,
      tcxt,
    };
    // }
  }
  // 跨境传输协议
  async function AddPipl(options, axiosClient) {

    let data = {
      method: 'POST',
      url: "https://account.live.com/API/ChinaPIPLAccrual",
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/530.35 (KHTML, like Gecko) Chrome/********* Safari/530.35",
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Content-Type': 'application/json',
        'sec-ch-ua-platform': '"Windows"',
        'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
        'sec-ch-ua-mobile': '?0',
        'uaid': options.uaid,
        'uiflvr': '1001',
        'x-ms-apitransport': 'xhr',
        'x-ms-apiversion': '2',
        'scid': '100232',
        'hpgid': '201087',
        'tcxt': options.tcxt,
        'canary': options.canary,
        'eipt': options.eipt,
        'wlprefeript': '1',
        'origin': 'https://account.live.com',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-mode': 'cors',
        'sec-fetch-dest': 'empty',
        'referer': 'https://account.live.com/pipl/accrue?mkt=ZH-CN&uiflavor=web&cobrandid=90015&id=292841&ru=https://login.live.com/login.srf%3fid%3d292841%26opid%3d871E525D1CF8C63C%26opidt%3d1742123195',
        'accept-language': 'zh-CN,zh;q=0.9',
        'priority': 'u=1, i',
      },
      data: {
        "uiflvr": 1001,
        "uaid": options.uaid,
        "scid": 100232,
        "hpgid": 201087
      }
    };

    let result = await axiosClient.request(data)
    // console.log(result.data, '绑定邮箱第5步')

    return result.data
  }
}

// 创建请求队列
const requestQueue = new Queue(
  async function (task, cb) {
    try {
      const { email, password, proofs, propassword } = task;
      const result = await performRegistration(
        email,
        password,
        proofs,
        propassword
      );
      cb(null, {
        email,
        ...result,
      });
    } catch (error) {
      cb(null, {
        success: false,
        email,
        error: error.message,
      });
    }
  },
  {
    concurrent: 10, // 同时处理5个请求
    maxRetries: 3, // 失败重试3次
    retryDelay: 2000, // 重试间隔2秒
  }
);

// 添加限流中间件
const limiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 60, // 限制60个请求
});

app.use("getmail", limiter);

// 修改路由处理
app.get("/getmail", async (req, res) => {
  const { email, password, proofs, propassword } = req.query;

  // 将请求加入队列
  requestQueue.push(
    {
      email,
      password,
      proofs,
      propassword,
    },
    function (err, result) {
      if (err) {
        console.error("处理失败:", err);
        res.status(500).json({
          success: false,
          email: email,
          error: err.message,
        });
      } else {
        res.json(result);
      }
    }
  );
});

// 添加健康检查接口
app.get("/health", (req, res) => {
  res.json({
    status: "ok",
    queueSize: requestQueue.length,
    memory: process.memoryUsage(),
  });
});

// 优化错误处理
app.use((err, req, res, next) => {
  console.error("全局错误:", err);
  res.status(500).json({
    success: false,
    error: "服务器内部错误",
  });
});

// PM2配置文件
// ecosystem.config.js
/*
module.exports = {
  apps: [{
    name: 'email-bind-service',
    script: 'server.js',
    instances: 2,
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
}
*/

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});
