#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Outlook邮箱Selenium解决方案
使用真实浏览器获取MSAuth令牌和邮件
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time
import json
import requests

class OutlookSeleniumSolution:
    def __init__(self, headless=False):
        """
        初始化Selenium WebDriver
        
        Args:
            headless: 是否使用无头模式
        """
        self.driver = None
        self.headless = headless
        self.session = requests.Session()
        
    def setup_driver(self):
        """设置Chrome WebDriver"""
        print("🔧 设置Chrome WebDriver...")
        
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # 添加必要的选项
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Chrome WebDriver设置成功")
            return True
        except Exception as e:
            print(f"❌ Chrome WebDriver设置失败: {e}")
            print("💡 请确保已安装Chrome浏览器和ChromeDriver")
            return False

    def login_to_outlook(self, username, password):
        """登录到Outlook"""
        print(f"🔐 开始登录Outlook: {username}")
        
        try:
            # 访问Outlook登录页面
            print("📱 访问Outlook登录页面...")
            self.driver.get("https://outlook.live.com/mail/")
            
            # 等待页面加载
            time.sleep(3)
            
            # 查找并填写用户名
            print("📝 填写用户名...")
            username_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "loginfmt"))
            )
            username_input.clear()
            username_input.send_keys(username)
            
            # 点击下一步
            next_button = self.driver.find_element(By.ID, "idSIButton9")
            next_button.click()
            
            # 等待密码输入框出现
            print("🔑 填写密码...")
            password_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "passwd"))
            )
            password_input.clear()
            password_input.send_keys(password)
            
            # 点击登录
            login_button = self.driver.find_element(By.ID, "idSIButton9")
            login_button.click()
            
            # 处理"保持登录状态"提示
            try:
                print("⏳ 处理保持登录状态...")
                stay_signed_in = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "idSIButton9"))
                )
                stay_signed_in.click()
            except:
                print("⚠️ 未出现保持登录状态提示")
            
            # 等待进入Outlook主页
            print("⏳ 等待进入Outlook主页...")
            WebDriverWait(self.driver, 30).until(
                lambda driver: "outlook.live.com" in driver.current_url and "mail" in driver.current_url
            )
            
            print("✅ 成功登录Outlook！")
            return True
            
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            return False

    def extract_auth_info(self):
        """从浏览器中提取认证信息"""
        print("🔍 提取认证信息...")
        
        try:
            # 获取所有Cookie
            cookies = self.driver.get_cookies()
            
            # 将Cookie添加到requests session
            for cookie in cookies:
                self.session.cookies.set(cookie['name'], cookie['value'], domain=cookie['domain'])
            
            print(f"✅ 提取了 {len(cookies)} 个Cookie")
            
            # 查找重要的Cookie
            important_cookies = ['MSPRequ', 'MSPOK', 'ClientId']
            for cookie in cookies:
                if cookie['name'] in important_cookies:
                    print(f"✅ 重要Cookie - {cookie['name']}: {cookie['value'][:30]}...")
            
            return True
            
        except Exception as e:
            print(f"❌ 提取认证信息失败: {e}")
            return False

    def intercept_network_requests(self):
        """拦截网络请求以获取MSAuth令牌"""
        print("🕵️ 拦截网络请求...")
        
        try:
            # 启用网络日志
            self.driver.execute_cdp_cmd('Network.enable', {})
            
            # 等待一些网络请求
            time.sleep(5)
            
            # 获取网络日志
            logs = self.driver.get_log('performance')
            
            # 查找包含MSAuth的请求
            for log in logs:
                message = json.loads(log['message'])
                if message['message']['method'] == 'Network.requestWillBeSent':
                    request = message['message']['params']['request']
                    headers = request.get('headers', {})
                    
                    if 'authorization' in headers:
                        auth_header = headers['authorization']
                        if 'MSAuth1.0' in auth_header:
                            print(f"✅ 找到MSAuth令牌: {auth_header[:50]}...")
                            return auth_header
            
            print("⚠️ 未在网络日志中找到MSAuth令牌")
            return None
            
        except Exception as e:
            print(f"❌ 拦截网络请求失败: {e}")
            return None

    def get_emails_with_selenium(self, max_emails=10):
        """使用Selenium获取邮件信息"""
        print(f"📧 使用Selenium获取邮件信息...")
        
        try:
            # 等待邮件列表加载
            print("⏳ 等待邮件列表加载...")
            time.sleep(10)
            
            # 查找邮件元素
            email_elements = self.driver.find_elements(By.CSS_SELECTOR, "[role='listitem']")
            
            if not email_elements:
                # 尝试其他选择器
                email_elements = self.driver.find_elements(By.CSS_SELECTOR, "[data-convid]")
            
            if not email_elements:
                # 再尝试其他选择器
                email_elements = self.driver.find_elements(By.CSS_SELECTOR, ".ms-List-cell")
            
            print(f"✅ 找到 {len(email_elements)} 个邮件元素")
            
            emails = []
            
            for i, element in enumerate(email_elements[:max_emails]):
                try:
                    # 提取邮件信息
                    email_info = self.extract_email_info(element)
                    if email_info:
                        emails.append(email_info)
                        print(f"✅ 提取邮件 {i+1}: {email_info['subject'][:30]}...")
                except Exception as e:
                    print(f"⚠️ 提取邮件 {i+1} 失败: {e}")
            
            return emails
            
        except Exception as e:
            print(f"❌ 获取邮件失败: {e}")
            return []

    def extract_email_info(self, element):
        """从邮件元素中提取信息"""
        try:
            # 尝试多种方法提取邮件信息
            subject = "未知主题"
            sender = "未知发件人"
            time_str = "未知时间"
            
            # 查找主题
            subject_elements = element.find_elements(By.CSS_SELECTOR, "[data-automation-id='subject']")
            if not subject_elements:
                subject_elements = element.find_elements(By.CSS_SELECTOR, ".ms-TooltipHost")
            if subject_elements:
                subject = subject_elements[0].text.strip()
            
            # 查找发件人
            sender_elements = element.find_elements(By.CSS_SELECTOR, "[data-automation-id='sender']")
            if not sender_elements:
                sender_elements = element.find_elements(By.CSS_SELECTOR, ".ms-Persona-primaryText")
            if sender_elements:
                sender = sender_elements[0].text.strip()
            
            # 查找时间
            time_elements = element.find_elements(By.CSS_SELECTOR, "[data-automation-id='time']")
            if not time_elements:
                time_elements = element.find_elements(By.CSS_SELECTOR, ".ms-MessageBar-text")
            if time_elements:
                time_str = time_elements[0].text.strip()
            
            if subject != "未知主题" or sender != "未知发件人":
                return {
                    'subject': subject,
                    'from': sender,
                    'time': time_str
                }
            
            return None
            
        except Exception as e:
            print(f"⚠️ 提取邮件信息失败: {e}")
            return None

    def complete_flow(self, username, password, max_emails=10):
        """完整的邮件获取流程"""
        try:
            print("开始Selenium邮件获取流程")
            print("=" * 50)
            
            # 设置WebDriver
            if not self.setup_driver():
                return []
            
            # 登录
            if not self.login_to_outlook(username, password):
                return []
            
            # 提取认证信息
            self.extract_auth_info()
            
            # 尝试拦截MSAuth令牌
            auth_token = self.intercept_network_requests()
            if auth_token:
                print(f"✅ 成功获取MSAuth令牌")
            
            # 获取邮件
            emails = self.get_emails_with_selenium(max_emails)
            
            print("=" * 50)
            print(f"📧 邮件获取完成！共获取 {len(emails)} 封邮件")
            
            return emails
            
        except Exception as e:
            print(f"❌ 完整流程失败: {str(e)}")
            return []
        finally:
            if self.driver:
                print("🔧 关闭浏览器...")
                self.driver.quit()

def main():
    """主函数"""
    print("Outlook邮箱Selenium解决方案")
    print("使用真实浏览器获取邮件")
    print("=" * 50)
    
    # 设置登录信息
    username = input("请输入邮箱地址: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return
    
    # 询问是否使用无头模式
    headless_choice = input("是否使用无头模式？(y/N): ").strip().lower()
    headless = headless_choice == 'y'
    
    # 获取邮件数量
    max_emails = input("请输入要获取的邮件数量 (默认10): ").strip()
    try:
        max_emails = int(max_emails) if max_emails else 10
    except:
        max_emails = 10
    
    # 创建Selenium客户端
    client = OutlookSeleniumSolution(headless=headless)
    
    try:
        # 执行完整流程
        emails = client.complete_flow(username, password, max_emails)
        
        # 显示结果
        if emails:
            print(f"\n📧 邮件列表 (共 {len(emails)} 封):")
            print("=" * 80)
            
            for i, email in enumerate(emails, 1):
                print(f"{i:2d}. {email['subject']}")
                print(f"     发件人: {email['from']}")
                print(f"     时间: {email['time']}")
                print("-" * 80)
        else:
            print("\n📧 没有获取到邮件")
            print("💡 可能的原因:")
            print("   - 收件箱为空")
            print("   - 页面元素选择器需要调整")
            print("   - 需要更长的等待时间")
            
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
